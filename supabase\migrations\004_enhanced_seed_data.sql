-- Enhanced seed data migration
-- This migration adds more comprehensive materials and test combinations

-- Add more materials for better testing variety
INSERT INTO materials (name, description, unit) 
SELECT 'Bitumen', 'Asphalt binder and bituminous materials', 'kg'
WHERE NOT EXISTS (SELECT 1 FROM materials WHERE name = 'Bitumen')
UNION ALL
SELECT 'Soil', 'Soil samples for geotechnical testing', 'kg'
WHERE NOT EXISTS (SELECT 1 FROM materials WHERE name = 'Soil')
UNION ALL
SELECT 'Water', 'Water quality testing for construction', 'liter'
WHERE NOT EXISTS (SELECT 1 FROM materials WHERE name = 'Water')
UNION ALL
SELECT 'Brick', 'Clay bricks and masonry units', 'piece'
WHERE NOT EXISTS (SELECT 1 FROM materials WHERE name = 'Brick')
UNION ALL
SELECT 'Tile', 'Ceramic and vitrified tiles', 'piece'
WHERE NOT EXISTS (SELECT 1 FROM materials WHERE name = 'Tile')
UNION ALL
SELECT 'Paint', 'Protective coatings and paints', 'liter'
WHERE NOT EXISTS (SELECT 1 FROM materials WHERE name = 'Paint');

-- Get all material IDs for comprehensive test types
DO $$
DECLARE
  cement_id UUID;
  concrete_id UUID;
  steel_id UUID;
  aggregate_id UUID;
  bitumen_id UUID;
  soil_id UUID;
  water_id UUID;
  brick_id UUID;
  tile_id UUID;
  paint_id UUID;
BEGIN
  SELECT id INTO cement_id FROM materials WHERE name = 'Cement';
  SELECT id INTO concrete_id FROM materials WHERE name = 'Concrete';
  SELECT id INTO steel_id FROM materials WHERE name = 'Steel';
  SELECT id INTO aggregate_id FROM materials WHERE name = 'Aggregate';
  SELECT id INTO bitumen_id FROM materials WHERE name = 'Bitumen';
  SELECT id INTO soil_id FROM materials WHERE name = 'Soil';
  SELECT id INTO water_id FROM materials WHERE name = 'Water';
  SELECT id INTO brick_id FROM materials WHERE name = 'Brick';
  SELECT id INTO tile_id FROM materials WHERE name = 'Tile';
  SELECT id INTO paint_id FROM materials WHERE name = 'Paint';

  -- Add missing aggregate test that was referenced but not inserted
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) 
  SELECT aggregate_id, 'AGG_GRAD', 'Gradation Test', 'Particle size distribution test', 350.00, 3
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'AGG_GRAD');

  -- Additional cement tests
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) 
  SELECT cement_id, 'CEM_CONSIST', 'Consistency Test', 'Normal consistency of cement paste', 250.00, 2
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'CEM_CONSIST')
  UNION ALL
  SELECT cement_id, 'CEM_SOUNDNESS', 'Soundness Test', 'Volume expansion test using Le Chatelier method', 350.00, 4
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'CEM_SOUNDNESS')
  UNION ALL
  SELECT cement_id, 'CEM_HEAT', 'Heat of Hydration', 'Heat generated during cement hydration', 600.00, 7
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'CEM_HEAT');

  -- Additional concrete tests
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) 
  SELECT concrete_id, 'CON_SPLIT', 'Split Tensile Strength', 'Indirect tensile strength test', 500.00, 8
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'CON_SPLIT')
  UNION ALL
  SELECT concrete_id, 'CON_FLEX', 'Flexural Strength', 'Modulus of rupture test', 550.00, 8
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'CON_FLEX')
  UNION ALL
  SELECT concrete_id, 'CON_DURABILITY', 'Durability Test', 'Permeability and durability assessment', 800.00, 14
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'CON_DURABILITY');

  -- Additional steel tests
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) 
  SELECT steel_id, 'STL_YIELD', 'Yield Strength', 'Proof stress and yield point determination', 700.00, 5
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'STL_YIELD')
  UNION ALL
  SELECT steel_id, 'STL_ELONGATION', 'Elongation Test', 'Percentage elongation at break', 400.00, 3
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'STL_ELONGATION')
  UNION ALL
  SELECT steel_id, 'STL_CHEMICAL', 'Chemical Analysis', 'Carbon, sulfur, phosphorus content analysis', 900.00, 7
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'STL_CHEMICAL');

  -- Bitumen tests
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) 
  SELECT bitumen_id, 'BIT_PENETRATION', 'Penetration Test', 'Penetration value at 25°C', 300.00, 3
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'BIT_PENETRATION')
  UNION ALL
  SELECT bitumen_id, 'BIT_SOFTENING', 'Softening Point', 'Ring and ball softening point test', 350.00, 3
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'BIT_SOFTENING')
  UNION ALL
  SELECT bitumen_id, 'BIT_DUCTILITY', 'Ductility Test', 'Ductility at 25°C', 400.00, 4
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'BIT_DUCTILITY')
  UNION ALL
  SELECT bitumen_id, 'BIT_VISCOSITY', 'Viscosity Test', 'Kinematic viscosity measurement', 450.00, 4
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'BIT_VISCOSITY');

  -- Soil tests
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) 
  SELECT soil_id, 'SOIL_GRAIN', 'Grain Size Analysis', 'Particle size distribution by sieve analysis', 400.00, 5
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'SOIL_GRAIN')
  UNION ALL
  SELECT soil_id, 'SOIL_LIQUID', 'Liquid Limit', 'Atterberg limits - liquid limit test', 300.00, 3
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'SOIL_LIQUID')
  UNION ALL
  SELECT soil_id, 'SOIL_PLASTIC', 'Plastic Limit', 'Atterberg limits - plastic limit test', 250.00, 3
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'SOIL_PLASTIC')
  UNION ALL
  SELECT soil_id, 'SOIL_COMPACTION', 'Compaction Test', 'Standard Proctor compaction test', 500.00, 5
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'SOIL_COMPACTION')
  UNION ALL
  SELECT soil_id, 'SOIL_CBR', 'CBR Test', 'California Bearing Ratio test', 600.00, 7
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'SOIL_CBR')
  UNION ALL
  SELECT soil_id, 'SOIL_PERMEABILITY', 'Permeability Test', 'Coefficient of permeability', 550.00, 6
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'SOIL_PERMEABILITY');

  -- Water tests
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) 
  SELECT water_id, 'WATER_PH', 'pH Test', 'pH value determination', 150.00, 2
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'WATER_PH')
  UNION ALL
  SELECT water_id, 'WATER_CHLORIDE', 'Chloride Content', 'Chloride ion concentration', 200.00, 3
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'WATER_CHLORIDE')
  UNION ALL
  SELECT water_id, 'WATER_SULFATE', 'Sulfate Content', 'Sulfate ion concentration', 250.00, 3
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'WATER_SULFATE')
  UNION ALL
  SELECT water_id, 'WATER_TDS', 'Total Dissolved Solids', 'TDS measurement', 180.00, 2
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'WATER_TDS')
  UNION ALL
  SELECT water_id, 'WATER_ORGANIC', 'Organic Impurities', 'Organic matter content', 300.00, 4
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'WATER_ORGANIC');

  -- Brick tests
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) 
  SELECT brick_id, 'BRICK_COMP', 'Compressive Strength', 'Compressive strength of brick units', 400.00, 5
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'BRICK_COMP')
  UNION ALL
  SELECT brick_id, 'BRICK_WATER', 'Water Absorption', 'Water absorption percentage', 250.00, 3
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'BRICK_WATER')
  UNION ALL
  SELECT brick_id, 'BRICK_EFFLORESCENCE', 'Efflorescence Test', 'Salt efflorescence assessment', 300.00, 7
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'BRICK_EFFLORESCENCE')
  UNION ALL
  SELECT brick_id, 'BRICK_DIMENSION', 'Dimensional Tolerance', 'Size and shape accuracy check', 200.00, 2
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'BRICK_DIMENSION');

  -- Tile tests
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) 
  SELECT tile_id, 'TILE_WATER', 'Water Absorption', 'Water absorption rate for tiles', 300.00, 4
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'TILE_WATER')
  UNION ALL
  SELECT tile_id, 'TILE_BREAKING', 'Breaking Strength', 'Modulus of rupture for tiles', 450.00, 5
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'TILE_BREAKING')
  UNION ALL
  SELECT tile_id, 'TILE_THERMAL', 'Thermal Shock', 'Thermal expansion and contraction test', 500.00, 6
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'TILE_THERMAL')
  UNION ALL
  SELECT tile_id, 'TILE_SLIP', 'Slip Resistance', 'Coefficient of friction test', 400.00, 4
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'TILE_SLIP');

  -- Paint tests
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) 
  SELECT paint_id, 'PAINT_VISCOSITY', 'Viscosity Test', 'Paint viscosity measurement', 250.00, 3
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'PAINT_VISCOSITY')
  UNION ALL
  SELECT paint_id, 'PAINT_OPACITY', 'Opacity Test', 'Hiding power and opacity', 300.00, 4
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'PAINT_OPACITY')
  UNION ALL
  SELECT paint_id, 'PAINT_ADHESION', 'Adhesion Test', 'Paint adhesion to substrate', 400.00, 5
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'PAINT_ADHESION')
  UNION ALL
  SELECT paint_id, 'PAINT_DURABILITY', 'Weather Resistance', 'UV and weather resistance test', 600.00, 10
  WHERE NOT EXISTS (SELECT 1 FROM test_types WHERE code = 'PAINT_DURABILITY');

END $$;

-- Create some sample test combinations (packages) that are commonly ordered together
-- This helps demonstrate the accordion functionality with realistic data

-- Update some test descriptions to be more detailed for better UX
UPDATE test_types SET description = 'Compressive strength test for cement as per IS 4031 (Part 6)' WHERE code = 'CEM_COMP';
UPDATE test_types SET description = 'Initial and final setting time as per IS 4031 (Part 5)' WHERE code = 'CEM_SET';
UPDATE test_types SET description = 'Fineness by Blaine air permeability method as per IS 4031 (Part 2)' WHERE code = 'CEM_FINE';
UPDATE test_types SET description = 'Compressive strength test after 7 days curing as per IS 516' WHERE code = 'CON_COMP_7';
UPDATE test_types SET description = 'Compressive strength test after 28 days curing as per IS 516' WHERE code = 'CON_COMP_28';
UPDATE test_types SET description = 'Workability test for fresh concrete as per IS 1199' WHERE code = 'CON_SLUMP';
UPDATE test_types SET description = 'Ultimate tensile strength test as per IS 1786' WHERE code = 'STL_TENS';
UPDATE test_types SET description = 'Bend test for ductility as per IS 1786' WHERE code = 'STL_BEND';
UPDATE test_types SET description = 'Aggregate crushing value test as per IS 2386 (Part 4)' WHERE code = 'AGG_CRUSH';
UPDATE test_types SET description = 'Aggregate impact value test as per IS 2386 (Part 4)' WHERE code = 'AGG_IMPACT';

SELECT 'Enhanced seed data migration completed successfully!' as status;