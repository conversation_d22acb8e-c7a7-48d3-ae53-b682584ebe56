'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/utils/supabase/client'
import { UserRole } from '@/lib/auth'
import { Loader2 } from 'lucide-react'

interface RoleGuardProps {
  allowedRoles: UserRole[]
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
}

export function RoleGuard({ 
  allowedRoles, 
  children, 
  fallback, 
  redirectTo = '/unauthorized' 
}: RoleGuardProps) {
  const [loading, setLoading] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    async function checkAccess() {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (!user) {
          router.push('/login')
          return
        }

        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('user_id', user.id)
          .single()

        if (profile && allowedRoles.includes(profile.role as UserRole)) {
          setHasAccess(true)
        } else {
          if (redirectTo) {
            router.push(redirectTo)
          } else {
            setHasAccess(false)
          }
        }
      } catch (error) {
        console.error('Role check error:', error)
        router.push('/login')
      } finally {
        setLoading(false)
      }
    }

    checkAccess()
  }, [allowedRoles, redirectTo, router, supabase])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!hasAccess) {
    return fallback || null
  }

  return <>{children}</>
}

// Convenience components for specific roles
export function AdminOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['admin']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function CustomerOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['customer']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function TechOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['tech']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function PickupOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['pickup']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function FinanceOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['finance']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function AuditorOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['auditor']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function StaffOnly({ children, fallback }: { children: React.ReactNode; fallback?: React.ReactNode }) {
  return (
    <RoleGuard allowedRoles={['admin', 'tech', 'pickup', 'finance', 'auditor']} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}