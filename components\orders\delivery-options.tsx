'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Truck, MapPin, Clock, IndianRupee } from 'lucide-react'

interface Address {
  line1: string
  city: string
  state: string
  pincode: string
}

interface DeliveryOptionsProps {
  selectedType: 'pickup' | 'dropoff' | null
  address?: Address
  onDeliveryChange: (type: 'pickup' | 'dropoff', address?: Address) => void
}

export function DeliveryOptions({ selectedType, address, onDeliveryChange }: DeliveryOptionsProps) {
  const [localAddress, setLocalAddress] = useState<Address>(address || {
    line1: '',
    city: '',
    state: '',
    pincode: ''
  })

  const handleAddressChange = (field: keyof Address, value: string) => {
    const updatedAddress = { ...localAddress, [field]: value }
    setLocalAddress(updatedAddress)
    
    // Only update parent if dropoff is selected and address is complete
    if (selectedType === 'dropoff') {
      onDeliveryChange('dropoff', updatedAddress)
    }
  }

  const handleDeliveryTypeSelect = (type: 'pickup' | 'dropoff') => {
    if (type === 'pickup') {
      onDeliveryChange('pickup')
    } else {
      onDeliveryChange('dropoff', localAddress)
    }
  }

  const isAddressComplete = () => {
    return localAddress.line1 && localAddress.city && localAddress.state && localAddress.pincode
  }

  return (
    <div className="space-y-6">
      {/* Delivery Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Pickup Option */}
        <Card
          className={`cursor-pointer transition-all hover:shadow-md ${
            selectedType === 'pickup'
              ? 'ring-2 ring-primary bg-primary/5'
              : 'hover:bg-accent/50'
          }`}
          onClick={() => handleDeliveryTypeSelect('pickup')}
        >
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <MapPin className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-lg">Lab Pickup</CardTitle>
                <CardDescription>Drop off samples at our lab</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="w-4 h-4" />
                <span>Available 24/7</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <IndianRupee className="w-4 h-4" />
                <span>No additional cost</span>
              </div>
              <div className="text-sm">
                <p className="font-medium mb-1">Lab Address:</p>
                <p className="text-muted-foreground">
                  123 Science Park Drive<br />
                  Research City, RC 12345
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Drop-off Option */}
        <Card
          className={`cursor-pointer transition-all hover:shadow-md ${
            selectedType === 'dropoff'
              ? 'ring-2 ring-primary bg-primary/5'
              : 'hover:bg-accent/50'
          }`}
          onClick={() => handleDeliveryTypeSelect('dropoff')}
        >
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Truck className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <CardTitle className="text-lg">Sample Pickup</CardTitle>
                <CardDescription>We collect samples from your location</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="w-4 h-4" />
                <span>Next business day</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <IndianRupee className="w-4 h-4" />
                <span>₹500 pickup fee</span>
              </div>
              <div className="text-sm text-muted-foreground">
                Convenient pickup service available within 50 miles of our lab.
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Address Form for Drop-off */}
      {selectedType === 'dropoff' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Pickup Address</CardTitle>
            <CardDescription>
              Please provide the address where we should collect your samples
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="line1">Street Address</Label>
                <Input
                  id="line1"
                  placeholder="123 Main Street"
                  value={localAddress.line1}
                  onChange={(e) => handleAddressChange('line1', e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  placeholder="City"
                  value={localAddress.city}
                  onChange={(e) => handleAddressChange('city', e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  placeholder="State"
                  value={localAddress.state}
                  onChange={(e) => handleAddressChange('state', e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="pincode">Pincode</Label>
                <Input
                  id="pincode"
                  placeholder="400001"
                  value={localAddress.pincode}
                  onChange={(e) => handleAddressChange('pincode', e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
            
            {!isAddressComplete() && (
              <div className="mt-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                <p className="text-sm text-amber-800 dark:text-amber-200">
                  Please complete all address fields to proceed.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Selection Summary */}
      {selectedType && (
        <div className="p-4 bg-primary/10 rounded-lg border border-primary/20">
          <div className="flex items-center gap-2">
            {selectedType === 'pickup' ? (
              <MapPin className="w-5 h-5 text-primary" />
            ) : (
              <Truck className="w-5 h-5 text-primary" />
            )}
            <span className="font-medium">
              Selected: {selectedType === 'pickup' ? 'Lab Pickup' : 'Sample Pickup'}
            </span>
            {selectedType === 'dropoff' && (
              <span className="text-sm text-muted-foreground ml-2">
                (+₹500 pickup fee)
              </span>
            )}
          </div>
          
          {selectedType === 'dropoff' && isAddressComplete() && (
            <div className="mt-2 text-sm text-muted-foreground">
              <p>Pickup Address:</p>
              <p>
                {localAddress.line1}<br />
                {localAddress.city}, {localAddress.state} {localAddress.pincode}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Help Text */}
      <div className="text-sm text-muted-foreground bg-muted/50 p-4 rounded-lg">
        <p className="font-medium mb-2">📋 Delivery Information:</p>
        <ul className="space-y-1 list-disc list-inside">
          <li><strong>Lab Pickup:</strong> Bring samples to our facility during business hours</li>
          <li><strong>Sample Pickup:</strong> We'll collect samples from your specified address</li>
          <li>Pickup service available Monday-Friday, 9 AM - 5 PM</li>
          <li>Samples must be properly labeled and packaged</li>
        </ul>
      </div>
    </div>
  )
}