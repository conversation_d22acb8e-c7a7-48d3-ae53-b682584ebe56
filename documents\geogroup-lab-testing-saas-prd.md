# GeoGroup Lab Testing SaaS - Product Requirements Document (PRD)

**Product**: Lab Testing Management Platform  
**Version**: MVP → V1  
**Date**: December 2024  
**Team**: Development Team  

---

## 1. Product Overview

### Vision
Create an end-to-end lab testing platform that enables clients to request material tests, track progress in real-time, and receive certified results while maintaining strict privacy controls for lab operations.

### Success Metrics
- Order completion rate > 95%
- Average test turnaround time < SLA
- Customer satisfaction score > 4.5/5
- Zero privacy breaches (technicians cannot access customer data)

---

## 2. User Stories & Testable Features

### Epic 1: User Authentication & Role Management

#### Feature 1.1: User Registration & Login
**User Story**: As a new customer, I want to register and login so I can access the platform.

**Acceptance Criteria**:
- [x] User can register with email/password
- [x] User receives email verification
- [x] User can login with valid credentials
- [x] Invalid login attempts are rejected
- [x] User profile is created with 'customer' role by default
- [x] Social login options (Google)

**Test Cases**:
```
TC1.1.1: Valid user registration
TC1.1.2: Duplicate email registration (should fail)
TC1.1.3: Valid login with correct credentials
TC1.1.4: Invalid login with wrong password
TC1.1.5: Email verification flow
```

#### Feature 1.2: Role-Based Access Control
**User Story**: As an admin, I want to assign roles to users so they can access appropriate features.

**Acceptance Criteria**:
- [x] Admin can assign roles: customer, tech, pickup, admin, finance, auditor
- [x] Users can only access features for their role
- [x] Role changes take effect immediately
- [x] Technicians cannot view customer information

**Test Cases**:
```
TC1.2.1: Admin assigns customer role
TC1.2.2: Admin assigns technician role
TC1.2.3: Technician cannot access customer data
TC1.2.4: Customer cannot access admin features
```

### Epic 2: Order Management

#### Feature 2.1: Create New Order
**User Story**: As a customer, I want to create a test order so I can get my materials tested.

**Acceptance Criteria**:
- [ ] Customer can select material type from catalog
- [ ] Customer can select test types for chosen material
- [ ] Customer can choose pickup or drop-off option
- [ ] Customer can select payment mode (pay now/pay on result)
- [ ] Order total is calculated correctly
- [ ] Order is saved in 'draft' status

**Test Cases**:
```
TC2.1.1: Create order with cement material and compressive strength test
TC2.1.2: Create order with pickup option
TC2.1.3: Create order with drop-off option
TC2.1.4: Create order with pay-now option
TC2.1.5: Create order with pay-on-result option
TC2.1.6: Verify order total calculation
```

#### Feature 2.2: Order Status Tracking
**User Story**: As a customer, I want to track my order status so I know the progress of my tests.

**Acceptance Criteria**:
- [ ] Customer can view order status in real-time
- [ ] Status updates are displayed with timestamps
- [ ] Customer receives notifications on status changes
- [ ] Order timeline shows: Created → Pickup → In Lab → Testing → Completed

**Test Cases**:
```
TC2.2.1: View order in 'draft' status
TC2.2.2: View order status progression
TC2.2.3: Receive notification on status change
TC2.2.4: View order timeline with timestamps
```

### Epic 3: Sample Management

#### Feature 3.1: Sample Code Generation
**User Story**: As the system, I want to generate unique sample codes so samples can be tracked anonymously.

**Acceptance Criteria**:
- [ ] System generates unique sample codes (format: MAT-YYYY-MM-NNN)
- [ ] Sample codes are human-readable
- [ ] Sample codes are sequential within material type
- [ ] Sample codes cannot be duplicated

**Test Cases**:
```
TC3.1.1: Generate sample code for cement (CEM-2024-12-001)
TC3.1.2: Generate sequential sample codes
TC3.1.3: Verify uniqueness of sample codes
TC3.1.4: Generate codes for different materials
```

#### Feature 3.2: Lab Intake Process
**User Story**: As a lab technician, I want to record sample receipt so I can start testing.

**Acceptance Criteria**:
- [ ] Technician can mark sample as received
- [ ] System records receipt timestamp
- [ ] Sample photo can be uploaded
- [ ] Order status updates to 'in_lab'
- [ ] Technician only sees sample code, not customer details

**Test Cases**:
```
TC3.2.1: Mark sample as received
TC3.2.2: Upload sample photo
TC3.2.3: Verify order status change to 'in_lab'
TC3.2.4: Verify technician cannot see customer info
```

### Epic 4: Test Execution

#### Feature 4.1: Test Assignment
**User Story**: As an admin, I want to assign tests to technicians so work can be distributed.

**Acceptance Criteria**:
- [ ] Admin can assign tests to available technicians
- [ ] Technician receives notification of assignment
- [ ] Test status updates to 'assigned'
- [ ] Only technicians from the same lab can be assigned

**Test Cases**:
```
TC4.1.1: Assign test to technician
TC4.1.2: Verify technician notification
TC4.1.3: Verify test status change
TC4.1.4: Cannot assign to technician from different lab
```

#### Feature 4.2: Test Result Recording
**User Story**: As a technician, I want to record test results so customers can receive their reports.

**Acceptance Criteria**:
- [ ] Technician can input test results in JSON format
- [ ] Technician can add remarks
- [ ] System validates result format against test schema
- [ ] Test status updates to 'completed'
- [ ] PDF report is generated automatically

**Test Cases**:
```
TC4.2.1: Record valid test results
TC4.2.2: Record results with remarks
TC4.2.3: Reject invalid result format
TC4.2.4: Verify test status change to 'completed'
TC4.2.5: Verify PDF generation
```

### Epic 5: Payment Processing

#### Feature 5.1: Payment Link Generation
**User Story**: As a customer, I want to receive payment links so I can pay for my tests.

**Acceptance Criteria**:
- [ ] System generates payment links for 'pay_now' orders
- [ ] System generates payment links for 'pay_on_result' after test completion
- [ ] Payment links expire after 24 hours
- [ ] Payment status is tracked in database

**Test Cases**:
```
TC5.1.1: Generate payment link for pay_now order
TC5.1.2: Generate payment link for pay_on_result order
TC5.1.3: Verify payment link expiration
TC5.1.4: Track payment status
```

#### Feature 5.2: Payment Webhook Processing
**User Story**: As the system, I want to process payment confirmations so orders can be completed.

**Acceptance Criteria**:
- [ ] System receives payment webhooks from provider
- [ ] Payment status updates to 'paid'
- [ ] Order status advances based on payment mode
- [ ] Customer receives confirmation email
- [ ] PDF results are emailed for completed payments

**Test Cases**:
```
TC5.2.1: Process successful payment webhook
TC5.2.2: Update payment status to 'paid'
TC5.2.3: Advance order status after payment
TC5.2.4: Send confirmation email
TC5.2.5: Email PDF results
```

### Epic 6: Pickup & Delivery

#### Feature 6.1: Pickup Assignment
**User Story**: As an admin, I want to assign pickups to agents so samples can be collected.

**Acceptance Criteria**:
- [ ] Admin can assign pickups to available agents
- [ ] Agent receives pickup details with customer address
- [ ] Pickup status updates to 'assigned'
- [ ] Agent can view pickup queue

**Test Cases**:
```
TC6.1.1: Assign pickup to agent
TC6.1.2: Agent receives pickup notification
TC6.1.3: Verify pickup status change
TC6.1.4: Agent views pickup queue
```

#### Feature 6.2: Pickup Status Updates
**User Story**: As a pickup agent, I want to update pickup status so everyone knows the progress.

**Acceptance Criteria**:
- [ ] Agent can update status: picked_up, in_transit, delivered
- [ ] Agent can upload proof photos
- [ ] Timestamps are recorded for each status
- [ ] Customer receives status notifications

**Test Cases**:
```
TC6.2.1: Update status to 'picked_up'
TC6.2.2: Update status to 'in_transit'
TC6.2.3: Update status to 'delivered'
TC6.2.4: Upload proof photo
TC6.2.5: Verify timestamp recording
```

### Epic 7: Reporting & Analytics

#### Feature 7.1: Customer Dashboard
**User Story**: As a customer, I want to see my order summary so I can track my testing activity.

**Acceptance Criteria**:
- [ ] Dashboard shows active orders count
- [ ] Dashboard shows awaiting payment count
- [ ] Dashboard shows recent results
- [ ] Dashboard shows spending chart by month

**Test Cases**:
```
TC7.1.1: Display active orders count
TC7.1.2: Display awaiting payment count
TC7.1.3: Display recent results
TC7.1.4: Display spending chart
```

#### Feature 7.2: Admin Analytics
**User Story**: As an admin, I want to see business metrics so I can monitor performance.

**Acceptance Criteria**:
- [ ] Admin can view orders by status
- [ ] Admin can view revenue metrics
- [ ] Admin can export data to CSV
- [ ] Admin can view lab utilization

**Test Cases**:
```
TC7.2.1: View orders by status chart
TC7.2.2: View revenue metrics
TC7.2.3: Export orders to CSV
TC7.2.4: View lab utilization metrics
```

### Epic 8: Privacy & Security

#### Feature 8.1: Data Privacy Controls
**User Story**: As a technician, I should only see anonymized data so customer privacy is protected.

**Acceptance Criteria**:
- [ ] Technicians can only access v_lab_workqueue view
- [ ] Customer names/contacts are not visible to technicians
- [ ] Only sample codes and test details are shown
- [ ] Attempts to access customer data are blocked

**Test Cases**:
```
TC8.1.1: Technician accesses lab workqueue
TC8.1.2: Technician cannot see customer names
TC8.1.3: Technician cannot access orders table
TC8.1.4: Block unauthorized data access
```

#### Feature 8.2: Audit Logging
**User Story**: As an auditor, I want to see all system activities so I can ensure compliance.

**Acceptance Criteria**:
- [ ] All data changes are logged with timestamps
- [ ] Actor information is recorded
- [ ] Before/after values are stored
- [ ] Audit logs are immutable
- [ ] Logs can be searched and filtered

**Test Cases**:
```
TC8.2.1: Log order creation
TC8.2.2: Log status changes
TC8.2.3: Log user actions
TC8.2.4: Search audit logs
TC8.2.5: Verify log immutability
```

---

## 3. Technical Requirements

### Performance
- Page load time < 2 seconds
- API response time < 500ms
- Support 100 concurrent users
- 99.9% uptime

### Security
- Row-Level Security (RLS) enabled
- HTTPS encryption
- Input validation and sanitization
- Regular security audits

### Scalability
- Horizontal scaling capability
- Database optimization
- CDN for static assets
- Caching strategy

---

## 4. Testing Strategy

### Automated Testing Levels
1. **Unit Tests**: Individual functions and components
2. **Integration Tests**: API endpoints with database
3. **E2E Tests**: Complete user workflows
4. **Security Tests**: RLS and access control

### Test Automation Tools
- **Frontend**: Jest, React Testing Library
- **Backend**: Supabase Test Client
- **E2E**: Playwright
- **API**: TestSprite
- **Security**: Custom RLS test suite

### Test Data Management
- Separate test database
- Automated test data setup/teardown
- Mock payment providers
- Sample file uploads

---

## 5. Definition of Done

For each feature to be considered complete:
- [ ] All acceptance criteria met
- [ ] Unit tests written and passing
- [ ] Integration tests written and passing
- [ ] E2E tests written and passing
- [ ] Security tests passing
- [ ] Code review completed
- [ ] Documentation updated
- [ ] Performance benchmarks met

---

## 6. Release Plan

### Phase 1: Core MVP (Weeks 1-4)
- User authentication
- Basic order creation
- Simple test workflow
- Payment integration

### Phase 2: Enhanced Features (Weeks 5-8)
- Pickup/delivery system
- Advanced reporting
- Real-time notifications
- PDF generation

### Phase 3: Polish & Scale (Weeks 9-12)
- Performance optimization
- Advanced analytics
- Mobile responsiveness
- Production deployment

---

## 7. Success Criteria

### MVP Success
- Complete order-to-result workflow functional
- All user roles can perform core tasks
- Payment processing working
- Basic reporting available

### V1 Success
- All features from build plan implemented
- 95% test coverage achieved
- Performance benchmarks met
- Security audit passed
- Customer feedback > 4.0/5

---

*This PRD serves as the foundation for development and testing. Each feature should be implemented incrementally with continuous testing to ensure quality and reliability.*