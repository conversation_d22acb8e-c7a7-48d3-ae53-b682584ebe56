-- Add delivery_type column to orders table
-- This migration adds the missing delivery_type column that the frontend expects

-- Create delivery type enum
DO $$ BEGIN
    CREATE TYPE delivery_type AS ENUM ('pickup', 'dropoff');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add delivery_type column to orders table
ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivery_type delivery_type;

-- Update existing records based on pickup_required field
UPDATE orders 
SET delivery_type = CASE 
    WHEN pickup_required = true THEN 'pickup'::delivery_type
    ELSE 'dropoff'::delivery_type
END
WHERE delivery_type IS NULL;

-- Make delivery_type NOT NULL after updating existing records
ALTER TABLE orders ALTER COLUMN delivery_type SET NOT NULL;

-- Set default value for new records
ALTER TABLE orders ALTER COLUMN delivery_type SET DEFAULT 'dropoff';

SELECT 'Added delivery_type column to orders table successfully!' as status;