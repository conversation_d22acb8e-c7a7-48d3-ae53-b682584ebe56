import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { getCurrentUser, hasRole, hasAnyRole, canAccessRoute, ROLE_ROUTES } from '@/lib/auth'
import type { AuthUser, UserRole } from '@/lib/auth'

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  redirect: vi.fn()
}))

// Mock Supabase server client
vi.mock('@/utils/supabase/server', () => ({
  createClient: vi.fn()
}))

const mockSupabase = {
  auth: {
    getUser: vi.fn()
  },
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn()
      }))
    }))
  }))
}

describe('Auth Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('hasRole', () => {
    it('should return true when user has the specified role', () => {
      const user: AuthUser = {
        id: '123',
        email: '<EMAIL>',
        profile: {
          user_id: '123',
          role: 'admin' as UserRole,
          company_id: null,
          lab_id: null,
          display_name: 'Test User',
          phone: null,
          created_at: '2024-01-01'
        }
      } as AuthUser

      expect(hasRole(user, 'admin')).toBe(true)
    })

    it('should return false when user has different role', () => {
      const user: AuthUser = {
        id: '123',
        email: '<EMAIL>',
        profile: {
          user_id: '123',
          role: 'customer' as UserRole,
          company_id: null,
          lab_id: null,
          display_name: 'Test User',
          phone: null,
          created_at: '2024-01-01'
        }
      } as AuthUser

      expect(hasRole(user, 'admin')).toBe(false)
    })

    it('should return false when user is null', () => {
      expect(hasRole(null, 'admin')).toBe(false)
    })

    it('should return false when user has no profile', () => {
      const user: AuthUser = {
        id: '123',
        email: '<EMAIL>'
      } as AuthUser

      expect(hasRole(user, 'admin')).toBe(false)
    })
  })

  describe('hasAnyRole', () => {
    it('should return true when user has one of the specified roles', () => {
      const user: AuthUser = {
        id: '123',
        email: '<EMAIL>',
        profile: {
          user_id: '123',
          role: 'admin' as UserRole,
          company_id: null,
          lab_id: null,
          display_name: 'Test User',
          phone: null,
          created_at: '2024-01-01'
        }
      } as AuthUser

      expect(hasAnyRole(user, ['admin', 'finance'])).toBe(true)
    })

    it('should return false when user has none of the specified roles', () => {
      const user: AuthUser = {
        id: '123',
        email: '<EMAIL>',
        profile: {
          user_id: '123',
          role: 'customer' as UserRole,
          company_id: null,
          lab_id: null,
          display_name: 'Test User',
          phone: null,
          created_at: '2024-01-01'
        }
      } as AuthUser

      expect(hasAnyRole(user, ['admin', 'finance'])).toBe(false)
    })

    it('should return false when user is null', () => {
      expect(hasAnyRole(null, ['admin', 'finance'])).toBe(false)
    })
  })

  describe('canAccessRoute', () => {
    it('should return true when admin can access any route', () => {
      const adminUser: AuthUser = {
        id: '123',
        email: '<EMAIL>',
        profile: {
          user_id: '123',
          role: 'admin' as UserRole,
          company_id: null,
          lab_id: null,
          display_name: 'Admin User',
          phone: null,
          created_at: '2024-01-01'
        }
      } as AuthUser

      expect(canAccessRoute(adminUser, '/dashboard')).toBe(true)
      expect(canAccessRoute(adminUser, '/admin')).toBe(true)
      expect(canAccessRoute(adminUser, '/lab')).toBe(true)
    })

    it('should return true when customer can access allowed routes', () => {
      const customerUser: AuthUser = {
        id: '123',
        email: '<EMAIL>',
        profile: {
          user_id: '123',
          role: 'customer' as UserRole,
          company_id: null,
          lab_id: null,
          display_name: 'Customer User',
          phone: null,
          created_at: '2024-01-01'
        }
      } as AuthUser

      expect(canAccessRoute(customerUser, '/dashboard')).toBe(true)
      expect(canAccessRoute(customerUser, '/orders')).toBe(true)
    })

    it('should return false when customer tries to access restricted routes', () => {
      const customerUser: AuthUser = {
        id: '123',
        email: '<EMAIL>',
        profile: {
          user_id: '123',
          role: 'customer' as UserRole,
          company_id: null,
          lab_id: null,
          display_name: 'Customer User',
          phone: null,
          created_at: '2024-01-01'
        }
      } as AuthUser

      expect(canAccessRoute(customerUser, '/admin')).toBe(false)
      expect(canAccessRoute(customerUser, '/lab')).toBe(false)
    })

    it('should return false when user is null', () => {
      expect(canAccessRoute(null, '/dashboard')).toBe(false)
    })

    it('should return false when user has no profile', () => {
      const user: AuthUser = {
        id: '123',
        email: '<EMAIL>'
      } as AuthUser

      expect(canAccessRoute(user, '/dashboard')).toBe(false)
    })
  })

  describe('ROLE_ROUTES', () => {
    it('should have correct routes for each role', () => {
      expect(ROLE_ROUTES.customer).toEqual(['/dashboard', '/orders', '/history'])
      expect(ROLE_ROUTES.tech).toEqual(['/lab'])
      expect(ROLE_ROUTES.pickup).toEqual(['/pickup'])
      expect(ROLE_ROUTES.admin).toEqual(['/admin', '/dashboard', '/orders', '/history', '/lab', '/pickup'])
      expect(ROLE_ROUTES.finance).toEqual(['/admin/finance', '/dashboard'])
      expect(ROLE_ROUTES.auditor).toEqual(['/admin/audit', '/dashboard'])
    })
  })
})