# GeoGroup Lab Testing SaaS — Build Plan (MVP → V1)

**Company**: Geo Group (India)  
**Date**: 20 Aug 2025  
**Tech Stack**: Next.js (React + App Router), Node runtime (for PDFs & email), Supabase (Auth, DB, RLS, Realtime, Storage), shadcn/ui, Vercel (hosting), Gmail via Nodemailer (mail), Playwright + TestSprite + <PERSON><PERSON>peteer (tests), MCP (Supabase) for local tooling.

---

## 0) TL;DR (What we’re building)
An end‑to‑end lab/material testing platform for clients (e.g., cement companies) to request tests, schedule pickup or drop‑off, pay now or upon results, track status in real‑time, and receive PDF reports by email. Internal roles (pickup agent, lab technician, admin/finance) process the workflow with strict privacy: technicians don’t see the customer identity, only anonymized sample IDs.

---

## 1) Roles & Personas
- **Customer** (Company contact): Creates orders, selects material & tests, chooses pickup vs drop‑off, pays, tracks status, downloads results.
- **Pickup Agent**: Gets assigned pickups with customer address; updates pickup → delivered to lab.
- **Lab Technician**: Sees assigned samples + tests (anonymized), records results, uploads PDFs, marks complete.
- **Admin/Dispatcher**: Oversees queue, assigns labs & agents, manages catalog (materials/tests/prices), issues refunds, dashboards.
- **Finance**: Views payments/earnings/expenses, exports.
- **Auditor (read‑only)**: Accesses immutable audit logs + reports.

> **Privacy rule**: Technicians must NEVER see customer identity or contact details.

---

## 2) Architecture Overview
- **Next.js 14+ (App Router)**: Frontend + API Route Handlers. Mostly Edge runtime; Node runtime where needed (PDF/email).
- **Supabase**: PostgreSQL + RLS, Auth, Realtime, Storage, optional Edge Functions. Enable **PostGIS** for nearest‑lab queries.
- **Storage buckets**: `sample-photos`, `results-pdfs`, `invoices`.
- **Realtime**: Supabase channels to push status updates to dashboards (customer & internal apps).
- **Email**: Nodemailer with Gmail (App Password or OAuth2). Server‑only.
- **Payments**: Provider‑agnostic interface. MVP can mock links; V1 target Razorpay (India) or Stripe. Payment webhooks handled in Next.js.
- **PDF generation**: `@react-pdf/renderer` or `pdfkit` in Node runtime route handler.
- **Queues/Jobs**: State transitions tracked in DB; retry via idempotent API + cron (Vercel Scheduled Functions) for stuck items.

---

## 3) Data Model (Supabase)
> Use **UUID v4** PKs, `created_at`/`updated_at` (default now()) on all tables.

### Core catalogs
- **companies**: id, name, gstin, note
- **addresses**: id, company_id? (nullable), label, line1..line3, city, state, pincode, country, lat, lon, geo (geography(Point,4326))
- **labs**: id, name, address_id FK, contact_email, contact_phone
- **materials**: id, name (e.g., "Cement"), description, unit
- **test_types**: id, material_id FK, code, name, description, base_price_inr numeric(12,2), sla_days int, schema jsonb (parameter/format defs)

### Users & roles
- **profiles**: user_id PK (auth.users), role enum('customer','tech','pickup','admin','finance','auditor'), company_id FK nullable, lab_id FK nullable, display_name, phone

### Orders & workflow
- **orders**: id, customer_id (profiles.user_id), company_id, pickup_required bool, pay_mode enum('pay_now','pay_on_result'), status enum('draft','pending_payment','confirmed','in_pickup','in_lab','testing','awaiting_payment','completed','cancelled'), total_inr numeric(12,2), currency text default 'INR', nearest_lab_id FK, notes
- **samples**: id, order_id FK, material_id FK, sample_code (short unique human ID, e.g., `CEM-2025-08-001`), qty numeric, weight numeric, received_at timestamptz, photo_path text
- **order_tests**: id, sample_id FK, test_type_id FK, status enum('queued','assigned','in_progress','failed','awaiting_payment','completed'), assigned_to (tech user_id), result_id FK nullable, price_inr numeric(12,2)
- **pickups**: id, order_id FK, pickup_address_id FK, drop_lab_id FK, pickup_agent_id (profiles.user_id) nullable, status enum('queued','assigned','picked_up','in_transit','delivered','cancelled'), scheduled_at, picked_up_at, delivered_at, proof_photo_path text
- **results**: id, order_test_id FK unique, result_json jsonb, remarks text, pdf_path text, generated_by (tech user_id), generated_at, approved_by (admin user_id), approved_at
- **payments**: id, order_id FK, amount numeric(12,2), provider text, provider_ref text, status enum('pending','paid','failed','refunded'), created_at, paid_at
- **expenses**: id, order_id FK nullable, lab_id FK nullable, category text, amount numeric(12,2), note text, created_by user_id
- **notifications**: id bigserial, user_id, type text, payload jsonb, read_at
- **audit_logs**: id bigserial, actor_id, action text, entity_type text, entity_id uuid, at timestamptz default now(), diff jsonb

### Views for privacy
- **v_lab_workqueue** *(tech‑safe)*: Join of `order_tests` → `samples` → `labs` that exposes only: test id, sample_code, material name, test code/name, instructions, due date, lab id, status. **No customer/company fields.**
- **v_pickup_queue**: Only fields the agent needs: pickup id, order id, pickup address (full), lab drop address, status.

> **Note**: technicians consume `v_lab_workqueue`; they are never permitted to select from `orders` directly.

---

## 4) Row‑Level Security (RLS) – Key Policies
Enable RLS on all tables. Examples (pseudo‑SQL):

```sql
-- profiles: user sees self; admin sees all
create policy "self_read" on profiles for select
  using (auth.uid() = user_id);
create policy "admin_all" on profiles for all
  using (exists(select 1 from profiles p where p.user_id = auth.uid() and p.role = 'admin'));

-- orders
create policy "customer_owns_order" on orders for select using (customer_id = auth.uid());
create policy "admin_all_orders" on orders for all using (is_admin(auth.uid()));

-- samples/order_tests/results restricted via joins or views
-- technicians: read via view only
create policy "tech_read_queue" on v_lab_workqueue for select using (exists(
  select 1 from profiles p where p.user_id = auth.uid() and p.role = 'tech'
));

-- pickups for agents
create policy "pickup_agent_reads_own" on pickups for select using (pickup_agent_id = auth.uid());

-- payments: customer sees own order payments
create policy "customer_payments" on payments for select using (
  exists(select 1 from orders o where o.id = order_id and o.customer_id = auth.uid())
);
```

> Implement `is_admin(uid uuid)` as a SQL function that checks `profiles.role='admin'`.

---

## 5) State Machines

### Order lifecycle
```mermaid
stateDiagram-v2
    [*] --> draft
    draft --> pending_payment: pay_mode=pay_now
    draft --> confirmed: pay_mode=pay_on_result
    pending_payment --> confirmed: payment.paid
    confirmed --> in_pickup: pickup_required=true
    confirmed --> in_lab: pickup_required=false (customer drops)
    in_pickup --> in_lab: delivered
    in_lab --> testing: lab intake
    testing --> awaiting_payment: all tests completed & pay_mode=pay_on_result
    testing --> completed: pay_mode=pay_now & paid
    awaiting_payment --> completed: payment.paid
    completed --> [*]
    [*] --> cancelled
```

### Test job lifecycle (`order_tests.status`)
`queued → assigned → in_progress → completed` (or `failed`).

### Pickup lifecycle
`queued → assigned → picked_up → in_transit → delivered` (or `cancelled`).

---

## 6) Nearest Lab Selection (PostGIS)
Enable PostGIS and store `addresses.geo`. Example helper:
```sql
create or replace function nearest_lab(address_id uuid)
returns uuid language sql stable as $$
  select l.id from labs l
  join addresses la on la.id = l.address_id
  join addresses ca on ca.id = address_id
  order by ST_Distance(la.geo, ca.geo) asc
  limit 1;
$$;
```
Call on order creation when `pickup_required=false` (customer will drop); otherwise use pickup address to compute destination lab.

---

## 7) API (Next.js Route Handlers)
> Paths under `/app/api/*`.

**Auth**
- Supabase Auth UI or custom shadcn forms. Roles assigned post‑signup by admin.

**Orders**
- `POST /api/orders` — create draft order (material + tests + pickup/drop + address). Returns priced order + payment intent (if pay_now).
- `GET /api/orders/:id` — customer’s own order details + test statuses.
- `POST /api/orders/:id/confirm` — finalizes order; triggers pickup assignment or lab intake.
- `POST /api/orders/:id/cancel`

**Pickups**
- `POST /api/pickups/:id/assign` (admin)
- `POST /api/pickups/:id/status` — agent updates statuses; upload proof photo.

**Lab intake & tests**
- `POST /api/lab/intake` — mark sample received (`samples.received_at`), moves order → `in_lab`.
- `POST /api/tests/:id/assign` (admin) — assign tech.
- `POST /api/tests/:id/start` — tech → `in_progress`.
- `POST /api/tests/:id/result` — tech uploads `result_json` and/or `pdf`; moves to `completed`. Triggers email/payment gate.

**Payments**
- `POST /api/payments/create-link` — returns a provider payment URL (mock or Razorpay/Stripe). Saves `payments` row `pending`.
- `POST /api/payments/webhook` — provider webhook → marks `paid` and advances order to `completed` + emails PDF.

**Reports/Exports**
- `GET /api/reports/orders.csv`, `GET /api/reports/finance.csv` (admin/finance)

**Notifications**
- `POST /api/notify` — internal helper to fan‑out realtime + email where applicable.

> All handlers must enforce role access via Supabase server client and RLS‑compatible SQL/Views.

---

## 8) Email Rules (Gmail via Nodemailer)
- Env: `GMAIL_USER`, `GMAIL_APP_PASSWORD` (or OAuth2). `MAIL_FROM="Geo Group Labs <<EMAIL>>"`.
- Templates: order confirmation, pickup assigned, sample received, test completed (paid vs payment link), receipt, passwordless sign‑in (if enabled).
- Results delivery logic:
  - If **pay_now**: send **result PDF** immediately after test completion and payment is confirmed.
  - If **pay_on_result**: send **payment link** first; upon successful payment, email **result PDF**.

---

## 9) UI Map (Next.js + shadcn/ui + Recharts)

**Public/Customer app**
- `/login` — Supabase Auth.
- `/dashboard` — cards: Active Orders, Awaiting Payment, Recent Results; charts for spend vs month.
- `/orders/new` — 3‑step wizard: (1) Material & Tests, (2) Pickup vs Drop + Address, (3) Review & Pay (now or later).
- `/orders/:id` — timeline: Created → Pickup → In Lab → Testing → Completed; per‑test status; payment CTA; download PDF(s).
- `/history` — past orders, filters, CSV export.

**Pickup app (role=pickup)**
- `/pickup/queue` — list + map; assign or accept jobs (configurable); update status; upload proof.

**Lab app (role=tech)**
- `/lab/queue` — cards from `v_lab_workqueue`; start/complete; upload results; preview PDF.

**Admin/Finance**
- `/admin/dispatch` — Kanban: Orders by state; assign lab/tech/agent.
- `/admin/catalog` — materials/tests/prices editor.
- `/admin/finance` — earnings, expenses (CRUD), payouts, refunds.
- `/admin/audit` — searchable audit log.

> Components: Use shadcn/ui (Button, Card, Tabs, Badge, Dialog, Form, Table, Toast). Charts via `recharts` (e.g., earnings over time, orders by state).

---

## 10) Pricing, Taxes, Invoices
- Each `order_tests` carries `price_inr`; `orders.total_inr` is sum (+ taxes). Keep simple GST fields on `orders` for invoice.
- Optional invoice PDF stored in `invoices` bucket; email to customer on payment.

---

## 11) PDF Generation
- Use a Node runtime route handler: `/api/results/:id/pdf` that builds and stores the PDF to `results-pdfs/ORDERTESTID.pdf`.
- Include: Geo Group branding, sample_code, test codes, measured values, method/standard, date, tech initials, digital signature (optional), QR code with verification URL.

---

## 12) Realtime & Notifications
- On table changes (orders, order_tests, pickups) publish to channels `orders:<customer_id>`, `lab:<lab_id>`, `pickup:<pickup_agent_id>`.
- Client subscribes after login to relevant channels.

---

## 13) Deployment
1. **Supabase**: create project, enable **PostGIS**, storage buckets, run schema SQL & RLS policies.
2. **Next/Vercel**: set env vars (Supabase URL/keys, Gmail, payment). Use Node runtime where needed.
3. **Domains**: map `app.geogroup.in` (customer), `internal.geogroup.in` (staff) or single app with role‑based routes.

---

## 14) Testing Strategy
- **Unit**: schema utils, pricing, state transitions.
- **Integration**: API handlers with Supabase test DB (TestSprite). Mock payment provider.
- **E2E (Playwright)**: Customer orders flow (pay_now + pay_on_result), Pickup updates, Tech completes tests, Email gate verified.
- **PDF**: Puppeteer snapshot tests on generated PDFs (render HTML → PDF optional path). Visual assertions on key text.
- **Access control**: RLS tests to ensure tech cannot query customer data.

---

## 15) Observability & Audit
- `audit_logs` for every state change (before/after diff jsonb).
- Error logging to console (Vercel), optionally Sentry.
- Metrics: orders per state, avg SLA, pickup times, lab throughput.

---

## 16) Supabase SQL (Starter DDL)
> Run via Supabase SQL editor or CLI. Trim if needed.

```sql
-- Enable PostGIS
create extension if not exists postgis;

-- Helper: is_admin(uid)
create or replace function is_admin(uid uuid)
returns boolean language sql stable as $$
  select exists(select 1 from profiles p where p.user_id = uid and p.role = 'admin');
$$;

-- Companies & Addresses
create table if not exists companies (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  gstin text,
  note text,
  created_at timestamptz default now()
);

create table if not exists addresses (
  id uuid primary key default gen_random_uuid(),
  company_id uuid references companies(id) on delete set null,
  label text,
  line1 text, line2 text, line3 text,
  city text, state text, pincode text, country text,
  lat double precision, lon double precision,
  geo geography(Point,4326),
  created_at timestamptz default now()
);

-- Labs
create table if not exists labs (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  address_id uuid references addresses(id) on delete set null,
  contact_email text, contact_phone text,
  created_at timestamptz default now()
);

-- Materials & Tests
create table if not exists materials (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  description text,
  unit text,
  created_at timestamptz default now()
);

create table if not exists test_types (
  id uuid primary key default gen_random_uuid(),
  material_id uuid references materials(id) on delete cascade,
  code text not null,
  name text not null,
  description text,
  base_price_inr numeric(12,2) not null default 0,
  sla_days int default 3,
  schema jsonb default '{}'::jsonb,
  created_at timestamptz default now()
);

-- Profiles
create table if not exists profiles (
  user_id uuid primary key references auth.users(id) on delete cascade,
  role text check (role in ('customer','tech','pickup','admin','finance','auditor')) not null,
  company_id uuid references companies(id) on delete set null,
  lab_id uuid references labs(id) on delete set null,
  display_name text,
  phone text,
  created_at timestamptz default now()
);

-- Orders
create table if not exists orders (
  id uuid primary key default gen_random_uuid(),
  customer_id uuid references profiles(user_id) not null,
  company_id uuid references companies(id),
  pickup_required boolean not null default false,
  pay_mode text check (pay_mode in ('pay_now','pay_on_result')) not null,
  status text check (status in ('draft','pending_payment','confirmed','in_pickup','in_lab','testing','awaiting_payment','completed','cancelled')) not null default 'draft',
  total_inr numeric(12,2) default 0,
  currency text default 'INR',
  nearest_lab_id uuid references labs(id),
  notes text,
  created_at timestamptz default now(),
  updated_at timestamptz default now()
);

create table if not exists samples (
  id uuid primary key default gen_random_uuid(),
  order_id uuid references orders(id) on delete cascade,
  material_id uuid references materials(id),
  sample_code text unique not null,
  qty numeric, weight numeric,
  received_at timestamptz,
  photo_path text,
  created_at timestamptz default now()
);

create table if not exists order_tests (
  id uuid primary key default gen_random_uuid(),
  sample_id uuid references samples(id) on delete cascade,
  test_type_id uuid references test_types(id),
  status text check (status in ('queued','assigned','in_progress','failed','awaiting_payment','completed')) not null default 'queued',
  assigned_to uuid references profiles(user_id) on delete set null,
  result_id uuid,
  price_inr numeric(12,2) not null default 0,
  created_at timestamptz default now()
);

create table if not exists pickups (
  id uuid primary key default gen_random_uuid(),
  order_id uuid references orders(id) on delete cascade,
  pickup_address_id uuid references addresses(id),
  drop_lab_id uuid references labs(id),
  pickup_agent_id uuid references profiles(user_id),
  status text check (status in ('queued','assigned','picked_up','in_transit','delivered','cancelled')) not null default 'queued',
  scheduled_at timestamptz,
  picked_up_at timestamptz,
  delivered_at timestamptz,
  proof_photo_path text,
  created_at timestamptz default now()
);

create table if not exists results (
  id uuid primary key default gen_random_uuid(),
  order_test_id uuid unique references order_tests(id) on delete cascade,
  result_json jsonb not null,
  remarks text,
  pdf_path text,
  generated_by uuid references profiles(user_id),
  generated_at timestamptz default now(),
  approved_by uuid references profiles(user_id),
  approved_at timestamptz
);

create table if not exists payments (
  id uuid primary key default gen_random_uuid(),
  order_id uuid references orders(id) on delete cascade,
  amount numeric(12,2) not null,
  provider text,
  provider_ref text,
  status text check (status in ('pending','paid','failed','refunded')) not null default 'pending',
  created_at timestamptz default now(),
  paid_at timestamptz
);

create table if not exists expenses (
  id uuid primary key default gen_random_uuid(),
  order_id uuid references orders(id) on delete set null,
  lab_id uuid references labs(id) on delete set null,
  category text,
  amount numeric(12,2) not null,
  note text,
  created_by uuid references profiles(user_id),
  created_at timestamptz default now()
);

create table if not exists notifications (
  id bigserial primary key,
  user_id uuid references profiles(user_id),
  type text,
  payload jsonb,
  read_at timestamptz,
  created_at timestamptz default now()
);

create table if not exists audit_logs (
  id bigserial primary key,
  actor_id uuid,
  action text,
  entity_type text,
  entity_id uuid,
  at timestamptz default now(),
  diff jsonb
);

-- Views (example for lab workqueue)
create or replace view v_lab_workqueue as
select
  ot.id as order_test_id,
  s.sample_code,
  m.name as material_name,
  tt.code as test_code,
  tt.name as test_name,
  ot.status,
  p.lab_id,
  (now() + (tt.sla_days || ' days')::interval) as due_at
from order_tests ot
join samples s on s.id = ot.sample_id
join materials m on m.id = s.material_id
join test_types tt on tt.id = ot.test_type_id
left join profiles p on p.user_id = ot.assigned_to;
```

> **Add RLS `enable row level security` on all base tables and `security_invoker` for views as needed.**

---

## 17) Example Result JSON (Cement)
```json
{
  "test_standard": "IS 4031 (Part 6): 1988",
  "compressive_strength_mpa": {
    "3_days": 28.5,
    "7_days": 39.2,
    "28_days": 52.1
  },
  "fineness_cm2_per_g": 3200,
  "initial_setting_time_min": 95,
  "final_setting_time_min": 210,
  "soundness_mm": 1.0,
  "remarks": "Meets OPC 43 grade requirements"
}
```

---

## 18) Env Vars & Config
```
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
GMAIL_USER=
GMAIL_APP_PASSWORD=
MAIL_FROM="Geo Group Labs <<EMAIL>>"
PAYMENT_PROVIDER=mock|razorpay|stripe
PAYMENT_WEBHOOK_SECRET=
APP_BASE_URL=https://app.geogroup.in
```

---

## 19) Project Scaffolding (commands)
```bash
# Next.js + shadcn/ui
npx create-next-app@latest geogroup-labs --ts --eslint --src-dir --app --tailwind
cd geogroup-labs
npx shadcn@latest init
npx shadcn@latest add button card input form table badge dialog toast tabs dropdown-menu avatar skeleton progress

# Supabase
npx supabase init
npx supabase link --project-ref <ref>
# Then: supabase db push (after adding the SQL above)

# Recharts, pdf, mail
pnpm add recharts nodemailer @react-pdf/renderer
pnpm add @supabase/supabase-js
```

---

## 20) Implementation Milestones
**M1 — Foundation (1–2 days)**
- Scaffold Next.js, auth, profiles, roles, RLS baseline, catalog pages (materials/tests), storage buckets.

**M2 — Order MVP (2–3 days)**
- Order wizard, nearest‑lab, pricing, pay_now mock, draft→confirmed.

**M3 — Pickup & Lab Queues (2–3 days)**
- Pickup assignment + status; tech queue via view; ingest sample; test status transitions.

**M4 — Results & Email Gate (2–3 days)**
- Result JSON + PDF; email logic for pay_now/pay_on_result; webhook to flip to completed.

**M5 — Dashboards & Finance (2 days)**
- Charts for earnings/expenses, exports, audit log.

**M6 — Hardening (ongoing)**
- Playwright E2E, PDF tests, RLS penetration tests, perf & UX polish.

---

## 21) Cursor/Windsurf “Do It” Prompts (copy‑paste)

**A. Create schema & policies**
> "Open `supabase/sql/001_init.sql` and paste the DDL from Section 16. Add `alter table ... enable row level security` for all tables. Create RLS policies from Section 4. Then run via CLI."

**B. Auth & Profiles**
> "In Next.js, add Supabase client helpers (server/client). On first login, upsert a `profiles` row with default role=customer. Build `/login` and `/dashboard`."

**C. Order Wizard**
> "Build `/orders/new` as a 3‑step wizard using shadcn/ui Forms. Step1: material + tests (fetch from `test_types`). Step2: pickup vs drop + address autocomplete (save to `addresses`). Step3: review & price + choose pay_now/pay_on_result. On submit, call `POST /api/orders`."

**D. Nearest Lab**
> "Implement a server action that calls SQL `nearest_lab(address_id)` and assigns `nearest_lab_id` on the order."

**E. Work Queues**
> "Create `/pickup/queue` and `/lab/queue` reading from `v_pickup_queue` and `v_lab_workqueue`. Add status update actions. Ensure tech view never queries `orders`."

**F. Results & PDF**
> "Implement `POST /api/tests/:id/result` that: validates role=tech, stores `result_json`, generates PDF (`/api/results/:id/pdf`), updates status, triggers email/payment gating."

**G. Payments (mock → provider)**
> "Create `POST /api/payments/create-link` that returns a mock URL which immediately marks `paid` in dev. Abstract provider so Razorpay can be swapped in. Add `/api/payments/webhook`."

**H. Email**
> "Add Nodemailer util. Implement `sendOrderConfirmation`, `sendPaymentLink`, `sendResultPdf`. Use server‑only module with `NODE_OPTIONS=--no-experimental-fetch` if needed."

**I. Realtime**
> "Publish changes on key table updates. Clients subscribe to `orders:<uid>` etc. Update timelines live."

**J. Tests**
> "Write Playwright tests for both flows: pay_now and pay_on_result. Add TestSprite integration tests for RLS guarded reads/writes. Add Puppeteer PDF snapshot test."

---

## 22) Nice‑to‑Haves (Post‑MVP)
- SMS/WhatsApp notifications (customer pickup ETA, result ready).
- Digital signatures & QR verification portal (`/verify/:sample_code`).
- Rate limiting + SSO for enterprise clients.
- Mobile PWA for pickup/tech.
- Multi‑tenant (external partner labs).

---

## 23) Acceptance Criteria (MVP)
- Customer can create order, choose pickup/drop, select tests, and either pay now or later.
- Realtime tracking shows: Pickup → In Lab → Testing → Completed.
- Technician can only see anonymized sample/test info and upload results.
- If unpaid & pay_on_result, customer receives payment link; upon payment, gets result PDF via email.
- Admin/Finance can view dashboards, earnings/expenses, and export CSV.
- RLS prevents cross‑tenant data leaks; technicians cannot access customer identity.

---

**End of Plan v1**
