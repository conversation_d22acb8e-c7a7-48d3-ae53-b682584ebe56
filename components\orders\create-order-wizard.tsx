'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ChevronLeft, ChevronRight, Package, TestTube, Truck, CreditCard } from 'lucide-react'
import { MaterialSelection } from './material-selection'
import { TestTypeSelection } from './test-type-selection'
import { DeliveryOptions } from './delivery-options'
import { PaymentOptions } from './payment-options'
import { OrderSummary } from './order-summary'

type OrderStep = 'material' | 'tests' | 'delivery' | 'payment' | 'summary'

interface OrderData {
  materials: Array<{ id: string; name: string }>
  testTypes: Array<{ id: string; name: string; price: number }>
  deliveryType: 'pickup' | 'dropoff' | null
  address?: {
    line1: string
    city: string
    state: string
    pincode: string
  }
  paymentMode: 'pay_now' | 'pay_on_result' | null
  totalAmount: number
}

const steps: Array<{
  id: OrderStep
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
}> = [
  {
    id: 'material',
    title: 'Select Material',
    description: 'Choose the type of material to test',
    icon: Package
  },
  {
    id: 'tests',
    title: 'Choose Tests',
    description: 'Select which tests to perform',
    icon: TestTube
  },
  {
    id: 'delivery',
    title: 'Delivery Method',
    description: 'Pickup or drop-off options',
    icon: Truck
  },
  {
    id: 'payment',
    title: 'Payment Mode',
    description: 'Choose when to pay',
    icon: CreditCard
  },
  {
    id: 'summary',
    title: 'Review Order',
    description: 'Confirm and submit your order',
    icon: Package
  }
]

export function CreateOrderWizard() {
  const [currentStep, setCurrentStep] = useState<OrderStep>('material')
  const [orderData, setOrderData] = useState<OrderData>({
    materials: [],
    testTypes: [],
    deliveryType: null,
    paymentMode: null,
    totalAmount: 0
  })

  const currentStepIndex = steps.findIndex(step => step.id === currentStep)
  const isFirstStep = currentStepIndex === 0
  const isLastStep = currentStepIndex === steps.length - 1

  const canProceed = () => {
    switch (currentStep) {
      case 'material':
        return orderData.materials.length > 0
      case 'tests':
        return orderData.testTypes.length > 0
      case 'delivery':
        return !!orderData.deliveryType
      case 'payment':
        return !!orderData.paymentMode
      default:
        return true
    }
  }

  const handleNext = () => {
    if (canProceed() && !isLastStep) {
      const nextStepIndex = currentStepIndex + 1
      setCurrentStep(steps[nextStepIndex].id)
    }
  }

  const handlePrevious = () => {
    if (!isFirstStep) {
      const prevStepIndex = currentStepIndex - 1
      setCurrentStep(steps[prevStepIndex].id)
    }
  }

  const updateOrderData = (updates: Partial<OrderData>) => {
    setOrderData(prev => {
      const updated = { ...prev, ...updates }
      // Recalculate total when test types change
      if (updates.testTypes) {
        updated.totalAmount = updates.testTypes.reduce((sum, test) => sum + test.price, 0)
      }
      return updated
    })
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 'material':
        return (
          <MaterialSelection
            selectedMaterialIds={orderData.materials.map(m => m.id)}
            onMaterialSelect={(materials) => 
              updateOrderData({ materials, testTypes: [] })
            }
          />
        )
      case 'tests':
        return (
          <TestTypeSelection
            materialIds={orderData.materials.map(m => m.id)}
            selectedTests={orderData.testTypes}
            onTestsChange={(testTypes) => updateOrderData({ testTypes })}
          />
        )
      case 'delivery':
        return (
          <DeliveryOptions
            selectedType={orderData.deliveryType}
            address={orderData.address}
            onDeliveryChange={(deliveryType, address) => 
              updateOrderData({ deliveryType, address })
            }
          />
        )
      case 'payment':
        return (
          <PaymentOptions
            selectedMode={orderData.paymentMode}
            totalAmount={orderData.totalAmount}
            onPaymentModeChange={(paymentMode) => updateOrderData({ paymentMode })}
          />
        )
      case 'summary':
        return (
          <OrderSummary
            orderData={orderData}
            onSubmit={() => {
              // Handle order submission
              console.log('Submitting order:', orderData)
            }}
          />
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-8">
      {/* Progress Steps */}
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const Icon = step.icon
          const isActive = step.id === currentStep
          const isCompleted = index < currentStepIndex
          
          return (
            <div key={step.id} className="flex items-center">
              <div className="flex flex-col items-center">
                <div className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                  ${isActive ? 'bg-primary border-primary text-primary-foreground' : ''}
                  ${isCompleted ? 'bg-green-500 border-green-500 text-white' : ''}
                  ${!isActive && !isCompleted ? 'border-muted-foreground/30 text-muted-foreground' : ''}
                `}>
                  <Icon className="w-5 h-5" />
                </div>
                <div className="mt-2 text-center">
                  <div className={`text-sm font-medium ${
                    isActive ? 'text-foreground' : 'text-muted-foreground'
                  }`}>
                    {step.title}
                  </div>
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className={`
                  flex-1 h-0.5 mx-4 transition-colors
                  ${isCompleted ? 'bg-green-500' : 'bg-muted-foreground/30'}
                `} />
              )}
            </div>
          )
        })}
      </div>

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle>{steps[currentStepIndex].title}</CardTitle>
          <CardDescription>{steps[currentStepIndex].description}</CardDescription>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={isFirstStep}
        >
          <ChevronLeft className="w-4 h-4 mr-2" />
          Previous
        </Button>
        
        {!isLastStep ? (
          <Button
            onClick={handleNext}
            disabled={!canProceed()}
          >
            Next
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        ) : (
          <Badge variant="secondary" className="px-4 py-2">
            Ready to submit
          </Badge>
        )}
      </div>
    </div>
  )
}