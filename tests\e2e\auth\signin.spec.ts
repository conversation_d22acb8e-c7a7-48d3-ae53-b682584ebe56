import { test, expect } from '@playwright/test'

test.describe('User Signin Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('/login')
  })

  test('should complete signin with valid test credentials', async ({ page }) => {
    // Fill in login form with test credentials using id selectors
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'Test@12345')
    
    // Submit the form
    await page.click('button[type="submit"]')
    
    // Wait for navigation to dashboard
    await page.waitForURL('**/dashboard', { timeout: 10000 })
    
    // Verify we're on the dashboard
    await expect(page).toHaveURL(/.*dashboard/)
    
    // Verify user is logged in by checking for navbar user dropdown
    await expect(page.locator('button:has-text("<EMAIL>")')).toBeVisible()
  })

  test('should show error for invalid credentials', async ({ page }) => {
    // Try to login with wrong password
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'WrongPassword123!')
    
    await page.click('button[type="submit"]')
    
    // Should show error message in alert
    await expect(page.locator('[role="alert"]')).toBeVisible()
    
    // Should remain on login page
    await expect(page).toHaveURL(/.*login/)
  })

  test('should show error for non-existent user', async ({ page }) => {
    // Try to login with non-existent email
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'Test@12345')
    
    await page.click('button[type="submit"]')
    
    // Should show error message in alert
    await expect(page.locator('[role="alert"]')).toBeVisible()
  })

  test('should show validation errors for empty fields', async ({ page }) => {
    // Try to submit empty form
    await page.click('button[type="submit"]')
    
    // Check for HTML5 validation - form should not submit
    await expect(page).toHaveURL(/.*login/)
    
    // Check that required fields show validation state
    await expect(page.locator('#email:invalid')).toBeVisible()
    await expect(page.locator('#password:invalid')).toBeVisible()
  })

  test('should show error for invalid email format', async ({ page }) => {
    await page.fill('#email', 'invalid-email')
    await page.fill('#password', 'Test@12345')
    
    await page.click('button[type="submit"]')
    
    // Check for HTML5 email validation
    await expect(page.locator('#email:invalid')).toBeVisible()
    await expect(page).toHaveURL(/.*login/)
  })

  test('should navigate to signup page', async ({ page }) => {
    // Click on signup link
    await page.click('text=Sign up')
    
    // Should navigate to signup page
    await expect(page).toHaveURL(/.*signup/)
  })

  test('should show loading state during form submission', async ({ page }) => {
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'Test@12345')
    
    // Submit form and check for loading state
    await page.click('button[type="submit"]')
    
    // Check if button shows loading state (spinner icon)
    await expect(page.locator('button[type="submit"]')).toBeDisabled()
    await expect(page.locator('button[type="submit"] svg.animate-spin')).toBeVisible()
  })

  test('should handle password visibility toggle', async ({ page }) => {
    await page.fill('#password', 'Test@12345')
    
    // Password should be hidden by default
    await expect(page.locator('#password')).toHaveAttribute('type', 'password')
    
    // Click toggle button (eye icon)
    await page.click('button[type="button"]:has(svg)')
    
    // Password should now be visible
    await expect(page.locator('#password')).toHaveAttribute('type', 'text')
    
    // Click toggle again
    await page.click('button[type="button"]:has(svg)')
    
    // Password should be hidden again
    await expect(page.locator('#password')).toHaveAttribute('type', 'password')
  })

  test('should redirect authenticated user to dashboard', async ({ page }) => {
    // First login
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'Test@12345')
    await page.click('button[type="submit"]')
    
    // Wait for dashboard
    await page.waitForURL('**/dashboard')
    
    // Now try to visit login page again
    await page.goto('/login')
    
    // Should be redirected to dashboard
    await expect(page).toHaveURL(/.*dashboard/)
  })

  test('should maintain session after page refresh', async ({ page }) => {
    // Login first
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'Test@12345')
    await page.click('button[type="submit"]')
    
    await page.waitForURL('**/dashboard')
    
    // Refresh the page
    await page.reload()
    
    // Should still be on dashboard and logged in
    await expect(page).toHaveURL(/.*dashboard/)
    await expect(page.locator('button:has-text("<EMAIL>")')).toBeVisible()
  })

  test('should logout successfully', async ({ page }) => {
    // Login first
    await page.fill('#email', '<EMAIL>')
    await page.fill('#password', 'Test@12345')
    await page.click('button[type="submit"]')
    
    await page.waitForURL('**/dashboard')
    
    // Click user dropdown and logout
    await page.click('button:has-text("<EMAIL>")')
    await page.click('text=Sign Out')
    
    // Should be redirected to login page
    await expect(page).toHaveURL(/.*login/)
    
    // Try to access dashboard directly
    await page.goto('/dashboard')
    
    // Should be redirected back to login
    await expect(page).toHaveURL(/.*login/)
  })
})