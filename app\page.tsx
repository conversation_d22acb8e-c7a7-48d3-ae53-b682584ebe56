'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  BeakerIcon, 
  ClockIcon, 
  ShieldCheckIcon, 
  TruckIcon,
  CheckCircleIcon,
  StarIcon,
  ArrowRightIcon,
  PlayCircleIcon
} from '@heroicons/react/24/outline'

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 }
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

const scaleOnHover = {
  whileHover: { scale: 1.05 },
  whileTap: { scale: 0.95 }
}

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
      {/* Navigation */}
      <motion.nav 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="fixed top-0 w-full bg-white/80 dark:bg-slate-900/80 backdrop-blur-md border-b border-slate-200 dark:border-slate-700 z-50"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <BeakerIcon className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-slate-900 dark:text-white">GeoGroup Labs</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#services" className="text-slate-600 hover:text-blue-600 dark:text-slate-300 dark:hover:text-blue-400 transition-colors">Services</a>
              <a href="#features" className="text-slate-600 hover:text-blue-600 dark:text-slate-300 dark:hover:text-blue-400 transition-colors">Features</a>
              <a href="#testimonials" className="text-slate-600 hover:text-blue-600 dark:text-slate-300 dark:hover:text-blue-400 transition-colors">Reviews</a>
              <Link href="/login" passHref><Button variant="outline" className="mr-2">Sign In</Button></Link>
              <Link href="/signup" passHref><Button>Get Started</Button></Link>
            </div>
          </div>
        </div>
      </motion.nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div {...fadeInUp} className="text-center lg:text-left">
              <Badge className="mb-4 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                🚀 Professional Testing Platform
              </Badge>
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-white mb-6">
                  Advanced Material Testing
                  <span className="text-blue-600 block">Made Simple</span>
                </h1>
              <p className="text-xl text-slate-600 dark:text-slate-300 mb-8 max-w-2xl">
                Get certified test results for concrete, steel, soil, and construction materials with real-time tracking, 
                fast turnaround times, and complete transparency.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <motion.div {...scaleOnHover}>
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                    Start Testing Now
                    <ArrowRightIcon className="ml-2 h-5 w-5" />
                  </Button>
                </motion.div>
                <motion.div {...scaleOnHover}>
                  <Button variant="outline" size="lg" className="px-8 py-3">
                    <PlayCircleIcon className="mr-2 h-5 w-5" />
                    Watch Demo
                  </Button>
                </motion.div>
              </div>
            </motion.div>
            
            <motion.div 
              initial={{ opacity: 0, x: 60 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-2xl p-8 border border-slate-200 dark:border-slate-700">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-slate-900 dark:text-white">Order #LAB-2024-001</h3>
                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Completed</Badge>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      <span className="text-sm text-slate-600 dark:text-slate-300">Sample Collected</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      <span className="text-sm text-slate-600 dark:text-slate-300">Testing Complete</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      <span className="text-sm text-slate-600 dark:text-slate-300">Results Available</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4">Download Certificate</Button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-7xl mx-auto">
          <motion.div {...fadeInUp} className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                  Why Choose GeoGroup Labs?
                </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Experience the future of material testing with our advanced platform designed for construction professionals.
            </p>
          </motion.div>

          <motion.div 
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {[
              {
                icon: ClockIcon,
                title: "Fast Turnaround",
                description: "Get results in 24-48 hours with our streamlined testing process"
              },
              {
                icon: ShieldCheckIcon,
                title: "Certified Results",
                description: "All tests meet international standards with full certification"
              },
              {
                icon: TruckIcon,
                title: "Sample Pickup",
                description: "Free sample collection from your construction site"
              },
              {
                icon: BeakerIcon,
                title: "Advanced Testing",
                description: "State-of-the-art equipment for accurate material analysis"
              }
            ].map((feature, index) => (
              <motion.div key={index} variants={fadeInUp}>
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="text-center">
                    <feature.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div {...fadeInUp} className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Our Testing Services
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Comprehensive material testing solutions for all your construction needs.
            </p>
          </motion.div>

          <motion.div 
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {[
              {
                title: "Concrete Testing",
                tests: ["Compressive Strength", "Slump Test", "Air Content", "Density Test"],
                price: "Starting at ₹4500"
              },
              {
                title: "Steel Testing",
                tests: ["Tensile Strength", "Bend Test", "Chemical Analysis", "Hardness Test"],
                price: "Starting at ₹6500"
              },
              {
                title: "Soil Analysis",
                tests: ["Bearing Capacity", "Moisture Content", "Grain Size", "Plasticity Index"],
                price: "Starting at ₹5500"
              }
            ].map((service, index) => (
              <motion.div key={index} variants={fadeInUp}>
                <Card className="h-full hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <CardHeader>
                    <CardTitle className="text-2xl text-center">{service.title}</CardTitle>
                    <CardDescription className="text-center text-2xl font-bold text-blue-600">
                      {service.price}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {service.tests.map((test, testIndex) => (
                        <li key={testIndex} className="flex items-center space-x-3">
                          <CheckCircleIcon className="h-5 w-5 text-green-500 flex-shrink-0" />
                          <span className="text-slate-600 dark:text-slate-300">{test}</span>
                        </li>
                      ))}
                    </ul>
                    <Button className="w-full mt-6">Order Now</Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900">
        <div className="max-w-7xl mx-auto">
          <motion.div {...fadeInUp} className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Trusted by Industry Leaders
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-300">
              See what our clients say about our testing services.
            </p>
          </motion.div>

          <motion.div 
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid md:grid-cols-3 gap-8"
          >
            {[
              {
                name: "Sarah Johnson",
                role: "Project Manager, BuildCorp",
                content: "GeoGroup Labs has been our go-to testing partner for 3 years. Their fast turnaround and accurate results keep our projects on schedule.",
                rating: 5
              },
              {
                name: "Michael Chen",
                role: "Civil Engineer, Infrastructure Ltd",
                content: "The real-time tracking feature is game-changing. We always know exactly where our samples are in the testing process.",
                rating: 5
              },
              {
                name: "Emily Rodriguez",
                role: "Quality Control Manager, MegaBuild",
                content: "Professional service, certified results, and excellent customer support. Highly recommend for any construction project.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <motion.div key={index} variants={fadeInUp}>
                <Card className="h-full">
                  <CardContent className="pt-6">
                    <div className="flex mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <StarIcon key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <p className="text-slate-600 dark:text-slate-300 mb-6 italic">
                      &ldquo;{testimonial.content}&rdquo;
                    </p>
                    <div>
                      <p className="font-semibold text-slate-900 dark:text-white">{testimonial.name}</p>
                      <p className="text-sm text-slate-500 dark:text-slate-400">{testimonial.role}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-blue-800">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div {...fadeInUp}>
            <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Ready to Start Testing?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join thousands of construction professionals who trust GeoGroup Labs for their material testing needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
              <Input 
                placeholder="Enter your email" 
                className="bg-white text-slate-900"
              />
              <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 px-8">
                Get Started Free
              </Button>
            </div>
            <p className="text-blue-200 text-sm mt-4">
              No credit card required • Free sample pickup • 24/7 support
            </p>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <BeakerIcon className="h-8 w-8 text-blue-400" />
                <span className="text-xl font-bold">GeoGroup Labs</span>
              </div>
              <p className="text-slate-400">
                Professional material testing services for the construction industry.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Services</h3>
              <ul className="space-y-2 text-slate-400">
                <li>Concrete Testing</li>
                <li>Steel Analysis</li>
                <li>Soil Testing</li>
                <li>Quality Control</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-slate-400">
                <li>About Us</li>
                <li>Careers</li>
                <li>Contact</li>
                <li>Support</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Legal</h3>
              <ul className="space-y-2 text-slate-400">
                <li>Privacy Policy</li>
                <li>Terms of Service</li>
                <li>Certifications</li>
                <li>Compliance</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-slate-800 mt-8 pt-8 text-center text-slate-400">
            <p>&copy; 2024 GeoGroup Labs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
