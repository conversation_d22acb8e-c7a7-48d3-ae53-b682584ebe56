-- Orders and Materials Schema Migration
-- This migration adds the core tables for order management

-- Create materials table
CREATE TABLE IF NOT EXISTS materials (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  unit TEXT DEFAULT 'kg',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create test_types table
CREATE TABLE IF NOT EXISTS test_types (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  material_id UUID REFERENCES materials(id) ON DELETE CASCADE,
  code TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  base_price_inr NUMERIC(12,2) NOT NULL DEFAULT 0,
  sla_days INTEGER DEFAULT 3,
  schema JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> addresses table
CREATE TABLE IF NOT EXISTS addresses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
  label TEXT,
  line1 TEXT NOT NULL,
  line2 TEXT,
  line3 TEXT,
  city TEXT NOT NULL,
  state TEXT NOT NULL,
  pincode TEXT NOT NULL,
  country TEXT DEFAULT 'India',
  lat NUMERIC(10,8),
  lon NUMERIC(11,8),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create order status enum
DO $$ BEGIN
    CREATE TYPE order_status AS ENUM (
        'draft',
        'pending_payment',
        'confirmed',
        'in_pickup',
        'in_lab',
        'testing',
        'awaiting_payment',
        'completed',
        'cancelled'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create payment mode enum
DO $$ BEGIN
    CREATE TYPE payment_mode AS ENUM ('pay_now', 'pay_on_result');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES profiles(user_id) NOT NULL,
  company_id UUID REFERENCES companies(id),
  pickup_required BOOLEAN NOT NULL DEFAULT false,
  pickup_address_id UUID REFERENCES addresses(id),
  pay_mode payment_mode NOT NULL,
  status order_status NOT NULL DEFAULT 'draft',
  total_inr NUMERIC(12,2) DEFAULT 0,
  currency TEXT DEFAULT 'INR',
  nearest_lab_id UUID REFERENCES labs(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create samples table
CREATE TABLE IF NOT EXISTS samples (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  material_id UUID REFERENCES materials(id),
  sample_code TEXT UNIQUE NOT NULL,
  qty NUMERIC,
  weight NUMERIC,
  received_at TIMESTAMP WITH TIME ZONE,
  photo_path TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create test status enum
DO $$ BEGIN
    CREATE TYPE test_status AS ENUM (
        'queued',
        'assigned',
        'in_progress',
        'failed',
        'awaiting_payment',
        'completed'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create order_tests table
CREATE TABLE IF NOT EXISTS order_tests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  sample_id UUID REFERENCES samples(id) ON DELETE CASCADE,
  test_type_id UUID REFERENCES test_types(id),
  status test_status NOT NULL DEFAULT 'queued',
  assigned_to UUID REFERENCES profiles(user_id) ON DELETE SET NULL,
  result_id UUID,
  price_inr NUMERIC(12,2) NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create pickup status enum
DO $$ BEGIN
    CREATE TYPE pickup_status AS ENUM (
        'queued',
        'assigned',
        'picked_up',
        'in_transit',
        'delivered',
        'cancelled'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create pickups table
CREATE TABLE IF NOT EXISTS pickups (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  pickup_address_id UUID REFERENCES addresses(id),
  drop_lab_id UUID REFERENCES labs(id),
  pickup_agent_id UUID REFERENCES profiles(user_id),
  status pickup_status NOT NULL DEFAULT 'queued',
  scheduled_at TIMESTAMP WITH TIME ZONE,
  picked_up_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  proof_photo_path TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create results table
CREATE TABLE IF NOT EXISTS results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_test_id UUID UNIQUE REFERENCES order_tests(id) ON DELETE CASCADE,
  result_json JSONB NOT NULL,
  remarks TEXT,
  pdf_path TEXT,
  generated_by UUID REFERENCES profiles(user_id),
  generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  approved_by UUID REFERENCES profiles(user_id),
  approved_at TIMESTAMP WITH TIME ZONE
);

-- Create payment status enum
DO $$ BEGIN
    CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'failed', 'refunded');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
  amount NUMERIC(12,2) NOT NULL,
  provider TEXT,
  provider_ref TEXT,
  status payment_status NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  paid_at TIMESTAMP WITH TIME ZONE
);

-- Enable Row Level Security
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE samples ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_tests ENABLE ROW LEVEL SECURITY;
ALTER TABLE pickups ENABLE ROW LEVEL SECURITY;
ALTER TABLE results ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for materials and test_types (public read)
CREATE POLICY "Public can read materials" ON materials
  FOR SELECT USING (true);

CREATE POLICY "Admins can manage materials" ON materials
  FOR ALL USING (public.is_admin(auth.uid()));

CREATE POLICY "Public can read test_types" ON test_types
  FOR SELECT USING (true);

CREATE POLICY "Admins can manage test_types" ON test_types
  FOR ALL USING (public.is_admin(auth.uid()));

-- RLS Policies for addresses
CREATE POLICY "Users can view own company addresses" ON addresses
  FOR SELECT USING (
    company_id IN (
      SELECT company_id FROM profiles WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create addresses" ON addresses
  FOR INSERT WITH CHECK (
    company_id IN (
      SELECT company_id FROM profiles WHERE user_id = auth.uid()
    ) OR company_id IS NULL
  );

CREATE POLICY "Admins can manage all addresses" ON addresses
  FOR ALL USING (public.is_admin(auth.uid()));

-- RLS Policies for orders
CREATE POLICY "Customers can view own orders" ON orders
  FOR SELECT USING (customer_id = auth.uid());

CREATE POLICY "Customers can create orders" ON orders
  FOR INSERT WITH CHECK (customer_id = auth.uid());

CREATE POLICY "Customers can update own draft orders" ON orders
  FOR UPDATE USING (customer_id = auth.uid() AND status = 'draft');

CREATE POLICY "Admins can manage all orders" ON orders
  FOR ALL USING (public.is_admin(auth.uid()));

-- RLS Policies for samples
CREATE POLICY "Customers can view own samples" ON samples
  FOR SELECT USING (
    order_id IN (
      SELECT id FROM orders WHERE customer_id = auth.uid()
    )
  );

CREATE POLICY "Admins can manage all samples" ON samples
  FOR ALL USING (public.is_admin(auth.uid()));

-- RLS Policies for order_tests
CREATE POLICY "Customers can view own order tests" ON order_tests
  FOR SELECT USING (
    sample_id IN (
      SELECT s.id FROM samples s
      JOIN orders o ON o.id = s.order_id
      WHERE o.customer_id = auth.uid()
    )
  );

CREATE POLICY "Techs can view assigned tests" ON order_tests
  FOR SELECT USING (
    assigned_to = auth.uid() OR
    EXISTS(
      SELECT 1 FROM profiles p
      WHERE p.user_id = auth.uid() AND p.role = 'tech'
    )
  );

CREATE POLICY "Admins can manage all order tests" ON order_tests
  FOR ALL USING (public.is_admin(auth.uid()));

-- RLS Policies for payments
CREATE POLICY "Customers can view own payments" ON payments
  FOR SELECT USING (
    order_id IN (
      SELECT id FROM orders WHERE customer_id = auth.uid()
    )
  );

CREATE POLICY "Admins can manage all payments" ON payments
  FOR ALL USING (public.is_admin(auth.uid()));

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_materials_name ON materials(name);
CREATE INDEX IF NOT EXISTS idx_test_types_material_id ON test_types(material_id);
CREATE INDEX IF NOT EXISTS idx_test_types_code ON test_types(code);
CREATE INDEX IF NOT EXISTS idx_addresses_company_id ON addresses(company_id);
CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_samples_order_id ON samples(order_id);
CREATE INDEX IF NOT EXISTS idx_samples_sample_code ON samples(sample_code);
CREATE INDEX IF NOT EXISTS idx_order_tests_sample_id ON order_tests(sample_id);
CREATE INDEX IF NOT EXISTS idx_order_tests_assigned_to ON order_tests(assigned_to);
CREATE INDEX IF NOT EXISTS idx_payments_order_id ON payments(order_id);

-- Insert sample materials and test types
INSERT INTO materials (name, description, unit) VALUES
  ('Cement', 'Portland cement and other cement types', 'kg'),
  ('Concrete', 'Ready mix concrete and concrete cubes', 'cubic meter'),
  ('Steel', 'Reinforcement steel bars and structural steel', 'kg'),
  ('Aggregate', 'Coarse and fine aggregates', 'kg')
ON CONFLICT DO NOTHING;

-- Get material IDs for test types
DO $$
DECLARE
  cement_id UUID;
  concrete_id UUID;
  steel_id UUID;
  aggregate_id UUID;
BEGIN
  SELECT id INTO cement_id FROM materials WHERE name = 'Cement';
  SELECT id INTO concrete_id FROM materials WHERE name = 'Concrete';
  SELECT id INTO steel_id FROM materials WHERE name = 'Steel';
  SELECT id INTO aggregate_id FROM materials WHERE name = 'Aggregate';

  -- Insert test types for cement
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) VALUES
    (cement_id, 'CEM_COMP', 'Compressive Strength', 'Compressive strength test for cement', 500.00, 7),
    (cement_id, 'CEM_SET', 'Setting Time', 'Initial and final setting time test', 300.00, 3),
    (cement_id, 'CEM_FINE', 'Fineness Test', 'Fineness by Blaine air permeability method', 400.00, 5)
  ON CONFLICT DO NOTHING;

  -- Insert test types for concrete
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) VALUES
    (concrete_id, 'CON_COMP_7', '7-Day Compressive Strength', 'Compressive strength test after 7 days curing', 600.00, 8),
    (concrete_id, 'CON_COMP_28', '28-Day Compressive Strength', 'Compressive strength test after 28 days curing', 700.00, 30),
    (concrete_id, 'CON_SLUMP', 'Slump Test', 'Workability test for fresh concrete', 200.00, 1)
  ON CONFLICT DO NOTHING;

  -- Insert test types for steel
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) VALUES
    (steel_id, 'STL_TENS', 'Tensile Strength', 'Ultimate tensile strength test', 800.00, 5),
    (steel_id, 'STL_BEND', 'Bend Test', 'Bend test for ductility', 400.00, 3),
    (steel_id, 'STL_WELD', 'Weld Test', 'Welding quality test', 600.00, 7)
  ON CONFLICT DO NOTHING;

  -- Insert test types for aggregate
  INSERT INTO test_types (material_id, code, name, description, base_price_inr, sla_days) VALUES
    (aggregate_id, 'AGG_GRAD', 'Gradation Test', 'Particle size distribution test', 350.00, 3),
    (aggregate_id, 'AGG_CRUSH', 'Crushing Value', 'Aggregate crushing value test', 450.00, 5),
    (aggregate_id, 'AGG_IMPACT', 'Impact Value', 'Aggregate impact value test', 400.00, 4)
  ON CONFLICT DO NOTHING;
END $$;

-- Create lab workqueue view for technicians (privacy-safe)
CREATE OR REPLACE VIEW v_lab_workqueue AS
SELECT
  ot.id as order_test_id,
  s.sample_code,
  m.name as material_name,
  tt.code as test_code,
  tt.name as test_name,
  tt.description as test_description,
  ot.status,
  ot.assigned_to,
  p.lab_id,
  (NOW() + (tt.sla_days || ' days')::interval) as due_at,
  ot.created_at
FROM order_tests ot
JOIN samples s ON s.id = ot.sample_id
JOIN materials m ON m.id = s.material_id
JOIN test_types tt ON tt.id = ot.test_type_id
LEFT JOIN profiles p ON p.user_id = ot.assigned_to;

-- Views do not support RLS policies directly. RLS on underlying tables applies.
-- Allow authenticated users to select from the view; underlying table policies still enforce access.
GRANT SELECT ON v_lab_workqueue TO authenticated;

SELECT 'Orders schema migration completed successfully!' as status;