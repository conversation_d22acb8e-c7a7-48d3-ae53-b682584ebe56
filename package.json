{"name": "lab-testing-software", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:all": "npm run test:coverage && npm run test:e2e", "test:install": "playwright install"}, "dependencies": {"@heroicons/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.55.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.540.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.2", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^5.0.1", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9", "eslint-config-next": "15.5.0", "jsdom": "^26.1.0", "playwright": "^1.54.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5", "vitest": "^3.2.4"}}