import { test, expect } from '@playwright/test'

test.describe('User Signup Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to signup page
    await page.goto('/signup')
  })

  test('should complete full signup process with test credentials', async ({ page }) => {
    // Fill in the signup form with test credentials using ID selectors
    await page.fill('#firstName', 'Test')
    await page.fill('#lastName', 'User')
    await page.fill('#email', '<EMAIL>')
    await page.fill('#phone', '1234567890')
    await page.fill('#companyName', 'Test Company')
    await page.fill('#companyAddress', 'Test Address')
    await page.fill('#password', 'Test@12345')
    await page.fill('#confirmPassword', 'Test@12345')
    
    // Accept terms and conditions
    await page.check('#terms')
    
    // Submit the form
    await page.click('button[type="submit"]')
    
    // Wait for navigation or success message
    await page.waitForURL('**/dashboard', { timeout: 10000 })
    
    // Verify we're on the dashboard
    await expect(page).toHaveURL(/.*dashboard/)
    
    // Verify user is logged in by checking for navbar user dropdown
    await expect(page.locator('button:has-text("<EMAIL>")')).toBeVisible()
  })

  test('should show validation errors for invalid inputs', async ({ page }) => {
    // Try to submit empty form
    await page.click('button[type="submit"]')
    
    // Check for HTML5 validation - form should not submit
    await expect(page).toHaveURL(/.*signup/)
    
    // Check that required fields show validation state
    await expect(page.locator('#firstName:invalid')).toBeVisible()
    await expect(page.locator('#email:invalid')).toBeVisible()
    await expect(page.locator('#password:invalid')).toBeVisible()
  })

  test('should show error for mismatched passwords', async ({ page }) => {
    await page.fill('#firstName', 'Test')
    await page.fill('#lastName', 'User')
    await page.fill('#email', '<EMAIL>')
    await page.fill('#companyName', 'Test Company')
    await page.fill('#password', 'Password123!')
    await page.fill('#confirmPassword', 'DifferentPassword123!')
    await page.check('#terms')
    
    await page.click('button[type="submit"]')
    
    await expect(page.locator('[role="alert"]:has-text("Passwords do not match")')).toBeVisible()
  })

  test('should show error for weak password', async ({ page }) => {
    await page.fill('#firstName', 'Test')
    await page.fill('#lastName', 'User')
    await page.fill('#email', '<EMAIL>')
    await page.fill('#companyName', 'Test Company')
    await page.fill('#password', '123')
    await page.fill('#confirmPassword', '123')
    await page.check('#terms')
    
    await page.click('button[type="submit"]')
    
    await expect(page.locator('[role="alert"]:has-text("Password must be at least 8 characters")')).toBeVisible()
  })

  test('should show error for invalid email format', async ({ page }) => {
    await page.fill('#firstName', 'Test')
    await page.fill('#lastName', 'User')
    await page.fill('#email', 'invalid-email')
    await page.fill('#companyName', 'Test Company')
    await page.fill('#password', 'ValidPassword123!')
    await page.fill('#confirmPassword', 'ValidPassword123!')
    await page.check('#terms')
    
    await page.click('button[type="submit"]')
    
    // Check for HTML5 email validation
    await expect(page.locator('#email:invalid')).toBeVisible()
    await expect(page).toHaveURL(/.*signup/)
  })

  test('should navigate to login page when clicking login link', async ({ page }) => {
    await page.click('text=Already have an account? Sign in')
    
    await expect(page).toHaveURL(/.*signin/)
  })

  test('should show loading state during form submission', async ({ page }) => {
    // Fill form with valid data
    await page.fill('#firstName', 'Test')
    await page.fill('#lastName', 'User')
    await page.fill('#email', '<EMAIL>')
    await page.fill('#companyName', 'Test Company')
    await page.fill('#password', 'ValidPassword123!')
    await page.fill('#confirmPassword', 'ValidPassword123!')
    await page.check('#terms')
    
    // Submit form
    await page.click('button[type="submit"]')
    
    // Check for loading state (spinner or disabled button)
    await expect(page.locator('button[type="submit"] svg')).toBeVisible()
  })

  test('should handle existing user error gracefully', async ({ page }) => {
    // Fill form with existing user email
    await page.fill('#firstName', 'Test')
    await page.fill('#lastName', 'User')
    await page.fill('#email', '<EMAIL>') // Use existing test user
    await page.fill('#companyName', 'Test Company')
    await page.fill('#password', 'ValidPassword123!')
    await page.fill('#confirmPassword', 'ValidPassword123!')
    await page.check('#terms')
    
    await page.click('button[type="submit"]')
    
    await expect(page.locator('[role="alert"]:has-text("User already exists")')).toBeVisible()
  })
})