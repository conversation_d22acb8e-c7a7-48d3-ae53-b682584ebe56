'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/utils/supabase/client'
import { UserRole } from '@/lib/auth'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Shield, Eye, EyeOff } from 'lucide-react'

interface PrivacyGuardProps {
  children: React.ReactNode
  dataType: 'customer' | 'financial' | 'personal' | 'sensitive'
  fallback?: React.ReactNode
}

// Define which roles can access which data types
const DATA_ACCESS_MATRIX: Record<string, UserRole[]> = {
  customer: ['customer', 'admin'], // Only customers can see their own data, admins can see all
  financial: ['finance', 'admin'], // Only finance and admin can see financial data
  personal: ['admin'], // Only admin can see personal data of others
  sensitive: ['admin', 'auditor'] // Only admin and auditor can see sensitive audit data
}

// Special privacy rules for technicians
const TECH_RESTRICTIONS = {
  // Technicians can only see:
  allowedFields: [
    'sample_id',
    'test_type',
    'test_parameters',
    'test_results',
    'test_status',
    'lab_notes',
    'created_at',
    'updated_at'
  ],
  // Technicians cannot see:
  restrictedFields: [
    'customer_name',
    'customer_email',
    'customer_phone',
    'company_name',
    'billing_address',
    'payment_info',
    'order_value',
    'customer_id'
  ]
}

export function PrivacyGuard({ children, dataType, fallback }: PrivacyGuardProps) {
  const [, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)
  const supabase = createClient()
  useEffect(() => {
    async function checkPrivacyAccess() {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (!user) {
          setHasAccess(false)
          setLoading(false)
          return
        }

        const { data: profile } = await supabase
          .from('profiles')
          .select('role')
          .eq('user_id', user.id)
          .single()

        if (profile) {
          const role = profile.role as UserRole
          setUserRole(role)
          
          // Check if user role has access to this data type
          const allowedRoles = DATA_ACCESS_MATRIX[dataType] || []
          setHasAccess(allowedRoles.includes(role))
        }
      } catch (error) {
        console.error('Privacy check error:', error)
        setHasAccess(false)
      } finally {
        setLoading(false)
      }
    }

    checkPrivacyAccess()
  }, [dataType, supabase])

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    )
  }

  if (!hasAccess) {
    return fallback || (
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          Access restricted. You don&apos;t have permission to view this {dataType} data.
        </AlertDescription>
      </Alert>
    )
  }

  return <>{children}</>
}

// Component to filter data for technicians
type GenericRecord = Record<string, unknown>
export function TechDataFilter<T extends GenericRecord>({ data, children }: { data: T | T[]; children: (filteredData: T | T[]) => React.ReactNode }) {
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()
  useEffect(() => {
    async function getUserRole() {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (user) {
          const { data: profile } = await supabase
            .from('profiles')
            .select('role')
            .eq('user_id', user.id)
            .single()

          if (profile) {
            setUserRole(profile.role as UserRole)
          }
        }
      } catch (error) {
        console.error('Error getting user role:', error)
      } finally {
        setLoading(false)
      }
    }

    getUserRole()
  }, [supabase])

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    )
  }

  // Filter data for technicians
  if (userRole === 'tech') {
    const filteredData = Array.isArray(data)
      ? (data as T[]).map(item => filterTechData(item))
      : filterTechData(data as T)

    return <>{children(filteredData)}</>
  }

  // Return unfiltered data for other roles
  return <>{children(data)}</>
}

// Helper function to filter data for technicians
function filterTechData<T extends GenericRecord>(item: T): T {
  if (!item || typeof item !== 'object') return item

  const filtered: GenericRecord = {}

  // Only include allowed fields for technicians
  TECH_RESTRICTIONS.allowedFields.forEach((field) => {
    if (field in item) {
      filtered[field] = item[field]
    }
  })

  // Add anonymized identifiers
  if (typeof item.customer_id === 'string') {
    filtered.customer_ref = `CUST-${item.customer_id.slice(-4)}`
  }

  if (typeof item.company_name === 'string') {
    filtered.company_ref = `COMP-${item.company_name.slice(0, 3).toUpperCase()}***`
  }

  return filtered as T
}

// Component to show/hide sensitive information
export function SensitiveInfo({
  children,
  level = 'medium'
}: {
  children: React.ReactNode
  level?: 'low' | 'medium' | 'high'
}) {
  const [isVisible, setIsVisible] = useState(false)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const supabase = createClient()
  useEffect(() => {
    async function getUserRole() {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (user) {
          const { data: profile } = await supabase
            .from('profiles')
            .select('role')
            .eq('user_id', user.id)
            .single()

          if (profile) {
            setUserRole(profile.role as UserRole)
          }
        }
      } catch (error) {
        console.error('Error getting user role:', error)
      }
    }

    getUserRole()
  }, [supabase])

  // Auto-show for admin and auditor roles
  const canAutoShow = userRole === 'admin' || (userRole === 'auditor' && level !== 'high')
  
  if (canAutoShow) {
    return <>{children}</>
  }

  // For other roles, require manual reveal
  return (
    <div className="relative">
      {isVisible ? (
        <div className="flex items-center space-x-2">
          {children}
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-gray-600"
            title="Hide sensitive information"
          >
            <EyeOff className="h-4 w-4" />
          </button>
        </div>
      ) : (
        <div className="flex items-center space-x-2">
          <span className="text-gray-400">••••••••</span>
          <button
            onClick={() => setIsVisible(true)}
            className="text-blue-600 hover:text-blue-800"
            title="Show sensitive information"
          >
            <Eye className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  )
}