import { redirect } from 'next/navigation'
import { getCurrentUser } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  FileText, 
  Truck, 
  FlaskConical, 
  DollarSign, 
  Shield,
  Package,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import Link from 'next/link'

export default async function DashboardPage() {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/login')
  }

  const roleConfig = {
    customer: {
      title: 'Customer Dashboard',
      description: 'Manage your lab testing orders and view results',
      color: 'bg-blue-500',
      icon: Users,
      quickActions: [
        { label: 'New Order', href: '/orders/new', icon: Package },
        { label: 'Order History', href: '/orders', icon: FileText },
        { label: 'Test Results', href: '/results', icon: CheckCircle }
      ]
    },
    tech: {
      title: 'Lab Technician Dashboard',
      description: 'Process samples and manage lab operations',
      color: 'bg-green-500',
      icon: FlaskConical,
      quickActions: [
        { label: 'Pending Tests', href: '/lab/pending', icon: Clock },
        { label: 'In Progress', href: '/lab/progress', icon: AlertCircle },
        { label: 'Completed Tests', href: '/lab/completed', icon: CheckCircle }
      ]
    },
    pickup: {
      title: 'Pickup Driver Dashboard',
      description: 'Manage sample pickups and deliveries',
      color: 'bg-orange-500',
      icon: Truck,
      quickActions: [
        { label: 'Scheduled Pickups', href: '/pickup/scheduled', icon: Clock },
        { label: 'Route Planning', href: '/pickup/routes', icon: Truck },
        { label: 'Delivery History', href: '/pickup/history', icon: FileText }
      ]
    },
    admin: {
      title: 'Administrator Dashboard',
      description: 'Full system management and oversight',
      color: 'bg-purple-500',
      icon: Shield,
      quickActions: [
        { label: 'User Management', href: '/admin/users', icon: Users },
        { label: 'System Reports', href: '/admin/reports', icon: FileText },
        { label: 'Lab Management', href: '/admin/labs', icon: FlaskConical }
      ]
    },
    finance: {
      title: 'Finance Dashboard',
      description: 'Financial operations and billing management',
      color: 'bg-emerald-500',
      icon: DollarSign,
      quickActions: [
        { label: 'Billing', href: '/admin/billing', icon: DollarSign },
        { label: 'Financial Reports', href: '/admin/finance/reports', icon: FileText },
        { label: 'Payment Processing', href: '/admin/payments', icon: CheckCircle }
      ]
    },
    auditor: {
      title: 'Auditor Dashboard',
      description: 'Audit trails and compliance monitoring',
      color: 'bg-red-500',
      icon: Shield,
      quickActions: [
        { label: 'Audit Logs', href: '/admin/audit/logs', icon: FileText },
        { label: 'Compliance Reports', href: '/admin/audit/compliance', icon: Shield },
        { label: 'System Health', href: '/admin/audit/health', icon: CheckCircle }
      ]
    }
  }

  // Get user role from profile, default to customer if no profile
  const userRole = user.profile?.role || 'customer'
  const config = roleConfig[userRole]
  const IconComponent = config.icon

  // Mock data - in real app, fetch from Supabase
  const stats = {
    customer: [
      { label: 'Active Orders', value: '3', change: '+2 this week' },
      { label: 'Completed Tests', value: '24', change: '+5 this month' },
      { label: 'Pending Results', value: '1', change: 'Expected today' }
    ],
    tech: [
      { label: 'Pending Tests', value: '12', change: '+3 today' },
      { label: 'In Progress', value: '5', change: 'Due in 2 hours' },
      { label: 'Completed Today', value: '8', change: '+2 vs yesterday' }
    ],
    pickup: [
      { label: 'Scheduled Pickups', value: '6', change: 'Today' },
      { label: 'Completed Routes', value: '15', change: 'This week' },
      { label: 'Pending Deliveries', value: '2', change: 'Due today' }
    ],
    admin: [
      { label: 'Total Users', value: '156', change: '+12 this month' },
      { label: 'Active Orders', value: '43', change: '+8 today' },
      { label: 'System Health', value: '99.9%', change: 'Uptime' }
    ],
    finance: [
      { label: 'Monthly Revenue', value: '$45,230', change: '+12% vs last month' },
      { label: 'Pending Invoices', value: '23', change: '$12,450 total' },
      { label: 'Collection Rate', value: '94.2%', change: '+2.1% this quarter' }
    ],
    auditor: [
      { label: 'Audit Events', value: '1,234', change: 'This month' },
      { label: 'Compliance Score', value: '98.5%', change: '+0.3% this week' },
      { label: 'Security Alerts', value: '0', change: 'All clear' }
    ]
  }

  const userStats = stats[userRole] || []

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className={`${config.color} text-white`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-white/20 rounded-lg">
                <IconComponent className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">{config.title}</h1>
                <p className="text-lg opacity-90">{config.description}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm opacity-75">Welcome back,</p>
              <p className="text-xl font-semibold">
                {user.profile?.display_name || user.email?.split('@')[0] || 'User'}
              </p>
              <Badge variant="secondary" className="mt-1">
                {userRole.charAt(0).toUpperCase() + userRole.slice(1)}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {userStats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="pb-2">
                <CardDescription>{stat.label}</CardDescription>
                <CardTitle className="text-2xl">{stat.value}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">{stat.change}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks for your role</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {config.quickActions.map((action, index) => {
                const ActionIcon = action.icon
                return (
                  <Link key={index} href={action.href}>
                    <Button variant="outline" className="w-full h-20 flex flex-col space-y-2">
                      <ActionIcon className="h-6 w-6" />
                      <span>{action.label}</span>
                    </Button>
                  </Link>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates and notifications</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                <div className="p-2 bg-blue-100 rounded-full">
                  <FileText className="h-4 w-4 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="font-medium">System notification</p>
                  <p className="text-sm text-gray-600">Welcome to GeoGroup Labs platform</p>
                </div>
                <Badge variant="secondary">New</Badge>
              </div>
              
              <div className="text-center py-8 text-gray-500">
                <p>No recent activity to display</p>
                <p className="text-sm">Activity will appear here as you use the system</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}