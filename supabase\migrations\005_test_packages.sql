-- Test Packages Migration
-- This migration adds test packages/combinations that are commonly ordered together

-- Create test_packages table
CREATE TABLE IF NOT EXISTS test_packages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  material_id UUID REFERENCES materials(id) ON DELETE CASCADE,
  discount_percentage NUMERIC(5,2) DEFAULT 0, -- Discount when buying as package
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create test_package_items table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS test_package_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  package_id UUID REFERENCES test_packages(id) ON DELETE CASCADE,
  test_type_id UUID REFERENCES test_types(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(package_id, test_type_id)
);

-- Enable Row Level Security
ALTER TABLE test_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_package_items ENABLE ROW LEVEL SECURITY;

-- RLS Policies for test_packages (public read)
CREATE POLICY "Public can read test_packages" ON test_packages
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage test_packages" ON test_packages
  FOR ALL USING (public.is_admin(auth.uid()));

-- RLS Policies for test_package_items (public read)
CREATE POLICY "Public can read test_package_items" ON test_package_items
  FOR SELECT USING (true);

CREATE POLICY "Admins can manage test_package_items" ON test_package_items
  FOR ALL USING (public.is_admin(auth.uid()));

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_test_packages_material_id ON test_packages(material_id);
CREATE INDEX IF NOT EXISTS idx_test_packages_active ON test_packages(is_active);
CREATE INDEX IF NOT EXISTS idx_test_package_items_package_id ON test_package_items(package_id);
CREATE INDEX IF NOT EXISTS idx_test_package_items_test_type_id ON test_package_items(test_type_id);

-- Insert sample test packages
DO $$
DECLARE
  cement_id UUID;
  concrete_id UUID;
  steel_id UUID;
  aggregate_id UUID;
  soil_id UUID;
  
  -- Package IDs
  cement_basic_pkg UUID;
  cement_complete_pkg UUID;
  concrete_standard_pkg UUID;
  concrete_advanced_pkg UUID;
  steel_basic_pkg UUID;
  steel_complete_pkg UUID;
  aggregate_standard_pkg UUID;
  soil_geotechnical_pkg UUID;
BEGIN
  -- Get material IDs
  SELECT id INTO cement_id FROM materials WHERE name = 'Cement';
  SELECT id INTO concrete_id FROM materials WHERE name = 'Concrete';
  SELECT id INTO steel_id FROM materials WHERE name = 'Steel';
  SELECT id INTO aggregate_id FROM materials WHERE name = 'Aggregate';
  SELECT id INTO soil_id FROM materials WHERE name = 'Soil';

  -- Create cement packages
  INSERT INTO test_packages (name, description, material_id, discount_percentage) VALUES
    ('Cement Basic Package', 'Essential cement tests for quality verification', cement_id, 10.0);
  
  INSERT INTO test_packages (name, description, material_id, discount_percentage) VALUES
    ('Cement Complete Package', 'Comprehensive cement testing suite', cement_id, 15.0);

  -- Get the package IDs
  SELECT id INTO cement_basic_pkg FROM test_packages WHERE name = 'Cement Basic Package';
  SELECT id INTO cement_complete_pkg FROM test_packages WHERE name = 'Cement Complete Package';

  -- Add tests to cement basic package
  INSERT INTO test_package_items (package_id, test_type_id)
  SELECT cement_basic_pkg, id FROM test_types 
  WHERE material_id = cement_id AND code IN ('CEM_COMP', 'CEM_SET', 'CEM_FINE');

  -- Add tests to cement complete package
  INSERT INTO test_package_items (package_id, test_type_id)
  SELECT cement_complete_pkg, id FROM test_types 
  WHERE material_id = cement_id;

  -- Create concrete packages
  INSERT INTO test_packages (name, description, material_id, discount_percentage) VALUES
    ('Concrete Standard Package', 'Standard concrete strength and workability tests', concrete_id, 12.0);
  
  INSERT INTO test_packages (name, description, material_id, discount_percentage) VALUES
    ('Concrete Advanced Package', 'Complete concrete analysis including durability', concrete_id, 18.0);

  SELECT id INTO concrete_standard_pkg FROM test_packages WHERE name = 'Concrete Standard Package';
  SELECT id INTO concrete_advanced_pkg FROM test_packages WHERE name = 'Concrete Advanced Package';

  -- Add tests to concrete standard package
  INSERT INTO test_package_items (package_id, test_type_id)
  SELECT concrete_standard_pkg, id FROM test_types 
  WHERE material_id = concrete_id AND code IN ('CON_COMP_7', 'CON_COMP_28', 'CON_SLUMP');

  -- Add tests to concrete advanced package
  INSERT INTO test_package_items (package_id, test_type_id)
  SELECT concrete_advanced_pkg, id FROM test_types 
  WHERE material_id = concrete_id;

  -- Create steel packages
  INSERT INTO test_packages (name, description, material_id, discount_percentage) VALUES
    ('Steel Basic Package', 'Essential steel mechanical properties', steel_id, 8.0);
  
  INSERT INTO test_packages (name, description, material_id, discount_percentage) VALUES
    ('Steel Complete Package', 'Full steel analysis including chemical composition', steel_id, 20.0);

  SELECT id INTO steel_basic_pkg FROM test_packages WHERE name = 'Steel Basic Package';
  SELECT id INTO steel_complete_pkg FROM test_packages WHERE name = 'Steel Complete Package';

  -- Add tests to steel basic package
  INSERT INTO test_package_items (package_id, test_type_id)
  SELECT steel_basic_pkg, id FROM test_types 
  WHERE material_id = steel_id AND code IN ('STL_TENS', 'STL_YIELD', 'STL_ELONGATION');

  -- Add tests to steel complete package
  INSERT INTO test_package_items (package_id, test_type_id)
  SELECT steel_complete_pkg, id FROM test_types 
  WHERE material_id = steel_id;

  -- Create aggregate package
  INSERT INTO test_packages (name, description, material_id, discount_percentage) VALUES
    ('Aggregate Standard Package', 'Complete aggregate quality assessment', aggregate_id, 10.0);

  SELECT id INTO aggregate_standard_pkg FROM test_packages WHERE name = 'Aggregate Standard Package';

  -- Add tests to aggregate package
  INSERT INTO test_package_items (package_id, test_type_id)
  SELECT aggregate_standard_pkg, id FROM test_types 
  WHERE material_id = aggregate_id;

  -- Create soil package
  INSERT INTO test_packages (name, description, material_id, discount_percentage) VALUES
    ('Soil Geotechnical Package', 'Comprehensive soil analysis for construction', soil_id, 15.0);

  SELECT id INTO soil_geotechnical_pkg FROM test_packages WHERE name = 'Soil Geotechnical Package';

  -- Add tests to soil package
  INSERT INTO test_package_items (package_id, test_type_id)
  SELECT soil_geotechnical_pkg, id FROM test_types 
  WHERE material_id = soil_id AND code IN ('SOIL_GRAIN', 'SOIL_LIQUID', 'SOIL_PLASTIC', 'SOIL_COMPACTION', 'SOIL_CBR');

END $$;

-- Create a view for packages with their tests and pricing
CREATE OR REPLACE VIEW v_test_packages_with_details AS
SELECT 
  tp.id as package_id,
  tp.name as package_name,
  tp.description as package_description,
  tp.material_id,
  m.name as material_name,
  tp.discount_percentage,
  tp.is_active,
  COUNT(tpi.test_type_id) as test_count,
  COALESCE(SUM(tt.base_price_inr), 0) as total_price_inr,
  COALESCE(SUM(tt.base_price_inr) * (1 - tp.discount_percentage / 100), 0) as discounted_price_inr,
  COALESCE(SUM(tt.base_price_inr) * (tp.discount_percentage / 100), 0) as savings_inr,
  MAX(tt.sla_days) as max_sla_days
FROM test_packages tp
JOIN materials m ON m.id = tp.material_id
LEFT JOIN test_package_items tpi ON tpi.package_id = tp.id
LEFT JOIN test_types tt ON tt.id = tpi.test_type_id
WHERE tp.is_active = true
GROUP BY tp.id, tp.name, tp.description, tp.material_id, m.name, tp.discount_percentage, tp.is_active;

-- Grant access to the view
GRANT SELECT ON v_test_packages_with_details TO authenticated;

SELECT 'Test packages migration completed successfully!' as status;