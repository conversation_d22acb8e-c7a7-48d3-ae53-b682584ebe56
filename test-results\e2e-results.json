{"config": {"configFile": "E:\\Projects\\lab-testing-software\\playwright.config.ts", "rootDir": "E:/Projects/lab-testing-software/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/e2e-results.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/Projects/lab-testing-software/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "E:/Projects/lab-testing-software/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Projects/lab-testing-software/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "E:/Projects/lab-testing-software/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Projects/lab-testing-software/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "E:/Projects/lab-testing-software/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "auth\\signin.spec.ts", "file": "auth/signin.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "User Signin Flow", "file": "auth/signin.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should complete signin with valid test credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 2230, "error": {"message": "Error: page.fill: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('#email')\u001b[22m\n", "stack": "Error: page.fill: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('#email')\u001b[22m\n\n    at E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts:11:16", "location": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test(\u001b[32m'should complete signin with valid test credentials'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// Fill in login form with test credentials using id selectors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'#email'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'#password'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Test@12345'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \n \u001b[90m 14 |\u001b[39m     \u001b[90m// Submit the form\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 11}, "message": "Error: page.fill: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('#email')\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test(\u001b[32m'should complete signin with valid test credentials'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// Fill in login form with test credentials using id selectors\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'#email'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'#password'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Test@12345'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \n \u001b[90m 14 |\u001b[39m     \u001b[90m// Submit the form\u001b[39m\u001b[0m\n\u001b[2m    at E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts:11:16\u001b[22m"}, {"message": "Error: browserContext._wrapApiCall: Target page, context or browser has been closed"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-20T10:56:17.344Z", "annotations": [], "attachments": [], "errorLocation": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "2a3c4c2dd3a93a755a7b-9aa3c076e66cc0c9ca36", "file": "auth/signin.spec.ts", "line": 9, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 2034, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-20T10:56:17.233Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2a3c4c2dd3a93a755a7b-5ed5307af8f8f895252b", "file": "auth/signin.spec.ts", "line": 27, "column": 7}, {"title": "should show error for non-existent user", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 2203, "error": {"message": "Error: page.fill: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('#email')\u001b[22m\n\u001b[2m    - locator resolved to <input value=\"\" id=\"email\" required=\"\" type=\"email\" data-slot=\"input\" placeholder=\"Enter your email\" class=\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled…/>\u001b[22m\n", "stack": "Error: page.fill: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('#email')\u001b[22m\n\u001b[2m    - locator resolved to <input value=\"\" id=\"email\" required=\"\" type=\"email\" data-slot=\"input\" placeholder=\"Enter your email\" class=\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled…/>\u001b[22m\n\n    at E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts:43:16", "location": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 43}, "snippet": "\u001b[0m \u001b[90m 41 |\u001b[39m   test(\u001b[32m'should show error for non-existent user'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 42 |\u001b[39m     \u001b[90m// Try to login with non-existent email\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 43 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'#email'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'#password'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Test@12345'\u001b[39m)\n \u001b[90m 45 |\u001b[39m     \n \u001b[90m 46 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[0m"}, "errors": [{"location": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 43}, "message": "Error: page.fill: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - waiting for locator('#email')\u001b[22m\n\u001b[2m    - locator resolved to <input value=\"\" id=\"email\" required=\"\" type=\"email\" data-slot=\"input\" placeholder=\"Enter your email\" class=\"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled…/>\u001b[22m\n\n\n\u001b[0m \u001b[90m 41 |\u001b[39m   test(\u001b[32m'should show error for non-existent user'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 42 |\u001b[39m     \u001b[90m// Try to login with non-existent email\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 43 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'#email'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'<EMAIL>'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'#password'\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'Test@12345'\u001b[39m)\n \u001b[90m 45 |\u001b[39m     \n \u001b[90m 46 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mclick(\u001b[32m'button[type=\"submit\"]'\u001b[39m)\u001b[0m\n\u001b[2m    at E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts:43:16\u001b[22m"}, {"message": "Error: browserContext._wrapApiCall: Test ended.\nBrowser logs:\n\n<launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1181\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,La<PERSON><PERSON><PERSON>e<PERSON>oading,LensO<PERSON>lay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-ktEWnd --remote-debugging-pipe --no-startup-window\n<launched> pid=25896\n[pid=25896] <gracefully close start>"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-20T10:56:17.331Z", "annotations": [], "attachments": [], "errorLocation": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 43}}], "status": "unexpected"}], "id": "2a3c4c2dd3a93a755a7b-b5930f8714a09f94a41c", "file": "auth/signin.spec.ts", "line": 41, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 1488, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-20T10:56:17.273Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "2a3c4c2dd3a93a755a7b-cac896fafe3c6f1380e7", "file": "auth/signin.spec.ts", "line": 52, "column": 7}, {"title": "should show error for invalid email format", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 873, "error": {"message": "Error: page.goto: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts:6:16", "location": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to login page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m   })\n \u001b[90m 8 |\u001b[39m\n \u001b[90m 9 |\u001b[39m   test(\u001b[32m'should complete signin with valid test credentials'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: Target page, context or browser has been closed\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to login page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m   })\n \u001b[90m 8 |\u001b[39m\n \u001b[90m 9 |\u001b[39m   test(\u001b[32m'should complete signin with valid test credentials'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-20T10:56:19.012Z", "annotations": [], "attachments": [], "errorLocation": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "2a3c4c2dd3a93a755a7b-01fe2e5791fa574b39bb", "file": "auth/signin.spec.ts", "line": 64, "column": 7}, {"title": "should navigate to signup page", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 381, "error": {"message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n    at E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts:6:16", "location": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 6}, "snippet": "\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to login page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m   })\n \u001b[90m 8 |\u001b[39m\n \u001b[90m 9 |\u001b[39m   test(\u001b[32m'should complete signin with valid test credentials'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: net::ERR_ABORTED at http://localhost:3000/login\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/login\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to login page\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m   })\n \u001b[90m 8 |\u001b[39m\n \u001b[90m 9 |\u001b[39m   test(\u001b[32m'should complete signin with valid test credentials'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts:6:16\u001b[22m"}, {"message": "Error: browserContext._wrapApiCall: Test ended.\nBrowser logs:\n\n<launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1181\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,La<PERSON><PERSON><PERSON>e<PERSON>oading,LensO<PERSON>lay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-Lwmu5q --remote-debugging-pipe --no-startup-window\n<launched> pid=11720\n[pid=11720] <gracefully close start>"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-20T10:56:19.563Z", "annotations": [], "attachments": [], "errorLocation": {"file": "E:\\Projects\\lab-testing-software\\tests\\e2e\\auth\\signin.spec.ts", "column": 16, "line": 6}}], "status": "unexpected"}], "id": "2a3c4c2dd3a93a755a7b-54ec2fa00405e7e57db8", "file": "auth/signin.spec.ts", "line": 75, "column": 7}, {"title": "should show loading state during form submission", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-fd225b6850b26875050a", "file": "auth/signin.spec.ts", "line": 83, "column": 7}, {"title": "should handle password visibility toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-36c668443806b2dc30db", "file": "auth/signin.spec.ts", "line": 95, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-666cab2c5e7bd67a0579", "file": "auth/signin.spec.ts", "line": 114, "column": 7}, {"title": "should maintain session after page refresh", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-d4c41607ecd683df607b", "file": "auth/signin.spec.ts", "line": 130, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-e91157e9fcbfb1aa42bc", "file": "auth/signin.spec.ts", "line": 146, "column": 7}, {"title": "should complete signin with valid test credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-ccdf64ab37814ae917f4", "file": "auth/signin.spec.ts", "line": 9, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-a849742fa404b2e8a595", "file": "auth/signin.spec.ts", "line": 27, "column": 7}, {"title": "should show error for non-existent user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-61e7191d4a518392a8e7", "file": "auth/signin.spec.ts", "line": 41, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-b4b96bb073eda19a4023", "file": "auth/signin.spec.ts", "line": 52, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-29a570126869f18a2939", "file": "auth/signin.spec.ts", "line": 64, "column": 7}, {"title": "should navigate to signup page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-cf0dadc1915c06f80a17", "file": "auth/signin.spec.ts", "line": 75, "column": 7}, {"title": "should show loading state during form submission", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-05124b409b013290322d", "file": "auth/signin.spec.ts", "line": 83, "column": 7}, {"title": "should handle password visibility toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-281b18368d8cf22398e6", "file": "auth/signin.spec.ts", "line": 95, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-ff059c3cca121c0c4830", "file": "auth/signin.spec.ts", "line": 114, "column": 7}, {"title": "should maintain session after page refresh", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-174709b2d85daf1bda28", "file": "auth/signin.spec.ts", "line": 130, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-d0c9a6bd226c4db6d6a9", "file": "auth/signin.spec.ts", "line": 146, "column": 7}, {"title": "should complete signin with valid test credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-86dcef7d5046a7da2197", "file": "auth/signin.spec.ts", "line": 9, "column": 7}, {"title": "should show error for invalid credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-f755d1ae326ab8cccca3", "file": "auth/signin.spec.ts", "line": 27, "column": 7}, {"title": "should show error for non-existent user", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-045806dc0ca3b229baa7", "file": "auth/signin.spec.ts", "line": 41, "column": 7}, {"title": "should show validation errors for empty fields", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-730d6c2dec83c829409d", "file": "auth/signin.spec.ts", "line": 52, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-b37c484fcb031b0e5ce7", "file": "auth/signin.spec.ts", "line": 64, "column": 7}, {"title": "should navigate to signup page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-ac7d117a6d5a69401344", "file": "auth/signin.spec.ts", "line": 75, "column": 7}, {"title": "should show loading state during form submission", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-e40a9f4972fc5a2864e4", "file": "auth/signin.spec.ts", "line": 83, "column": 7}, {"title": "should handle password visibility toggle", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-024fb1869c8479f9dfad", "file": "auth/signin.spec.ts", "line": 95, "column": 7}, {"title": "should redirect authenticated user to dashboard", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-87f7d3a5e5d72dbf85ad", "file": "auth/signin.spec.ts", "line": 114, "column": 7}, {"title": "should maintain session after page refresh", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-560936de2da5abe1c701", "file": "auth/signin.spec.ts", "line": 130, "column": 7}, {"title": "should logout successfully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2a3c4c2dd3a93a755a7b-c3cfab9bda2af9fef618", "file": "auth/signin.spec.ts", "line": 146, "column": 7}]}]}, {"title": "auth\\signup.spec.ts", "file": "auth/signup.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "User Signup Flow", "file": "auth/signup.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should complete full signup process with test credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-bbef7cf8788697c8c063", "file": "auth/signup.spec.ts", "line": 9, "column": 7}, {"title": "should show validation errors for invalid inputs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-d4b7420cf14488375ccf", "file": "auth/signup.spec.ts", "line": 36, "column": 7}, {"title": "should show error for mismatched passwords", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-5941af8d9cbd2a664a88", "file": "auth/signup.spec.ts", "line": 49, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-1fda6b37ec2b0e4ba593", "file": "auth/signup.spec.ts", "line": 63, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-458efa95481027e4d9f1", "file": "auth/signup.spec.ts", "line": 77, "column": 7}, {"title": "should navigate to login page when clicking login link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-4c4c86c1851c6debabd1", "file": "auth/signup.spec.ts", "line": 93, "column": 7}, {"title": "should show loading state during form submission", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-2a635486a84fc1f8d8ab", "file": "auth/signup.spec.ts", "line": 99, "column": 7}, {"title": "should handle existing user error gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-b7cdd4c50793790a6aa9", "file": "auth/signup.spec.ts", "line": 116, "column": 7}, {"title": "should complete full signup process with test credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-de90bcf95c415cef583f", "file": "auth/signup.spec.ts", "line": 9, "column": 7}, {"title": "should show validation errors for invalid inputs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-cd10a5359807c028e871", "file": "auth/signup.spec.ts", "line": 36, "column": 7}, {"title": "should show error for mismatched passwords", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-ab430d7127619a2f991b", "file": "auth/signup.spec.ts", "line": 49, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-6b645e8e6811acc8beab", "file": "auth/signup.spec.ts", "line": 63, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-c510bfe0d96d1c2f99b7", "file": "auth/signup.spec.ts", "line": 77, "column": 7}, {"title": "should navigate to login page when clicking login link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-da83d059d6aee3173aec", "file": "auth/signup.spec.ts", "line": 93, "column": 7}, {"title": "should show loading state during form submission", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-10e3817dd22145356e68", "file": "auth/signup.spec.ts", "line": 99, "column": 7}, {"title": "should handle existing user error gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-1e5826c741194c1f153e", "file": "auth/signup.spec.ts", "line": 116, "column": 7}, {"title": "should complete full signup process with test credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-bac076cd527982ca2236", "file": "auth/signup.spec.ts", "line": 9, "column": 7}, {"title": "should show validation errors for invalid inputs", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-a5858b06a779400b40ca", "file": "auth/signup.spec.ts", "line": 36, "column": 7}, {"title": "should show error for mismatched passwords", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-60084b72ac39915dd367", "file": "auth/signup.spec.ts", "line": 49, "column": 7}, {"title": "should show error for weak password", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-440ec9d918585f1a9465", "file": "auth/signup.spec.ts", "line": 63, "column": 7}, {"title": "should show error for invalid email format", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-8241f51990a471e103fe", "file": "auth/signup.spec.ts", "line": 77, "column": 7}, {"title": "should navigate to login page when clicking login link", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-301a5804c49a942bad9e", "file": "auth/signup.spec.ts", "line": 93, "column": 7}, {"title": "should show loading state during form submission", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-1bcd7c1ebe71b3228509", "file": "auth/signup.spec.ts", "line": 99, "column": 7}, {"title": "should handle existing user error gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c60d589c25b5f9baa21a-c3b76354b9b1e2878d58", "file": "auth/signup.spec.ts", "line": 116, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-08-20T10:56:15.192Z", "duration": 4970.634, "expected": 2, "skipped": 51, "unexpected": 4, "flaky": 0}}