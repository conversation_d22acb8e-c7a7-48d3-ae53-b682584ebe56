import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'
import { User } from '@supabase/supabase-js'

export type UserRole = 'customer' | 'tech' | 'pickup' | 'admin' | 'finance' | 'auditor'

export interface UserProfile {
  user_id: string
  role: UserRole
  company_id: string | null
  lab_id: string | null
  display_name: string | null
  phone: string | null
  created_at: string
}

export interface AuthUser extends User {
  profile?: UserProfile
}

// Get current user and profile from server component
export async function getCurrentUser(): Promise<AuthUser | null> {
  const supabase = await createClient()
  
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error || !user) {
    return null
  }

  // Get user profile
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('user_id', user.id)
    .single()

  return {
    ...user,
    profile: profile || undefined
  }
}

// Require authentication - redirect to login if not authenticated
export async function requireAuth(): Promise<AuthUser> {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/login')
  }
  
  return user
}

// Require specific role - redirect to unauthorized if wrong role
export async function requireRole(allowedRoles: UserRole[]): Promise<AuthUser> {
  const user = await requireAuth()
  
  if (!user.profile || !allowedRoles.includes(user.profile.role)) {
    redirect('/unauthorized')
  }
  
  return user
}

// Check if user has admin role
export async function isAdmin(): Promise<boolean> {
  const user = await getCurrentUser()
  return user?.profile?.role === 'admin'
}

// Check if user has specific role
export function hasRole(user: AuthUser | null, role: UserRole): boolean {
  return user?.profile?.role === role
}

// Check if user has any of the specified roles
export function hasAnyRole(user: AuthUser | null, roles: UserRole[]): boolean {
  return user?.profile ? roles.includes(user.profile.role) : false
}

// Role-based route protection
export const ROLE_ROUTES: Record<UserRole, string[]> = {
  customer: ['/dashboard', '/orders', '/history'],
  tech: ['/lab'],
  pickup: ['/pickup'],
  admin: ['/admin', '/dashboard', '/orders', '/history', '/lab', '/pickup'],
  finance: ['/admin/finance', '/dashboard'],
  auditor: ['/admin/audit', '/dashboard']
}

// Check if user can access route
export function canAccessRoute(user: AuthUser | null, path: string): boolean {
  if (!user?.profile) return false
  
  const allowedRoutes = ROLE_ROUTES[user.profile.role] || []
  return allowedRoutes.some(route => path.startsWith(route))
}