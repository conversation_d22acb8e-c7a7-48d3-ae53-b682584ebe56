import { createBrowserClient } from '@supabase/ssr'

export const createClient = () =>
  createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        flowType: 'implicit',
        storage: typeof window !== 'undefined' ? window.localStorage : undefined,
        storageKey: 'supabase.auth.token'
      }
    }
  )

// Database types
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          user_id: string
          role: 'customer' | 'tech' | 'pickup' | 'admin' | 'finance' | 'auditor'
          company_id: string | null
          lab_id: string | null
          display_name: string | null
          phone: string | null
          created_at: string
        }
        Insert: {
          user_id: string
          role: 'customer' | 'tech' | 'pickup' | 'admin' | 'finance' | 'auditor'
          company_id?: string | null
          lab_id?: string | null
          display_name?: string | null
          phone?: string | null
          created_at?: string
        }
        Update: {
          user_id?: string
          role?: 'customer' | 'tech' | 'pickup' | 'admin' | 'finance' | 'auditor'
          company_id?: string | null
          lab_id?: string | null
          display_name?: string | null
          phone?: string | null
          created_at?: string
        }
      }
      companies: {
        Row: {
          id: string
          name: string
          gstin: string | null
          note: string | null
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          gstin?: string | null
          note?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          gstin?: string | null
          note?: string | null
          created_at?: string
        }
      }
    }
  }
}