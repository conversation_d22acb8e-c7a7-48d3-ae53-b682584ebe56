'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search, Package } from 'lucide-react'
import { createClient } from '@/lib/supabase'

interface Material {
  id: string
  name: string
  description: string
  category: string
  is_active: boolean
}

interface MaterialSelectionProps {
  selectedMaterialIds?: string[]
  onMaterialSelect: (materials: Array<{ id: string; name: string }>) => void
}

export function MaterialSelection({ selectedMaterialIds = [], onMaterialSelect }: MaterialSelectionProps) {
  const [materials, setMaterials] = useState<Material[]>([])
  const [filteredMaterials, setFilteredMaterials] = useState<Material[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchMaterials()
  }, [])

  useEffect(() => {
    // Filter materials based on search term
    const filtered = materials.filter(material =>
      material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      material.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredMaterials(filtered)
  }, [materials, searchTerm])

  const fetchMaterials = async () => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from('materials')
        .select('*')
        .order('name', { ascending: true })

      if (error) throw error
      
      // Map DB rows (which don't have category/is_active) to the UI shape
      const normalized = (data || []).map((m: any) => ({
        id: m.id,
        name: m.name,
        description: m.description || '',
        // Derive a simple category from the material name for grouping
        category: (m.name || 'other').toLowerCase(),
        is_active: true
      })) as Material[]

      setMaterials(normalized)
    } catch (err) {
      console.error('Error fetching materials:', err)
      setError('Failed to load materials. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // No need to group materials anymore - use filteredMaterials directly

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-10 bg-muted rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-destructive mb-4">{error}</div>
        <Button onClick={fetchMaterials} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search materials by name, category, or description..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Materials Grid */}
      {filteredMaterials.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No materials found matching your search.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredMaterials.map((material) => {
                  const isSelected = selectedMaterialIds.includes(material.id)
                  const handleMaterialClick = () => {
                    let newSelectedMaterials
                    if (isSelected) {
                      // Remove from selection
                      newSelectedMaterials = selectedMaterialIds
                        .filter(id => id !== material.id)
                        .map(id => ({ 
                          id, 
                          name: materials.find(m => m.id === id)?.name || '' 
                        }))
                    } else {
                      // Add to selection
                      newSelectedMaterials = [...selectedMaterialIds, material.id]
                        .map(id => ({ 
                          id, 
                          name: materials.find(m => m.id === id)?.name || '' 
                        }))
                    }
                    onMaterialSelect(newSelectedMaterials)
                  }
                  
            return (
              <Card
                key={material.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected
                    ? 'ring-2 ring-primary bg-primary/5'
                    : 'hover:bg-accent/50'
                }`}
                onClick={handleMaterialClick}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-base">{material.name}</CardTitle>
                    {isSelected && (
                      <Badge variant="default" className="ml-2">
                        Selected
                      </Badge>
                    )}
                  </div>
                  <Badge variant="secondary" className="w-fit">
                    {material.category.replace('_', ' ')}
                  </Badge>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm">
                    {material.description}
                  </CardDescription>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* Selection Summary */}
      {selectedMaterialIds.length > 0 && (
        <div className="mt-6 p-4 bg-primary/10 rounded-lg border border-primary/20">
          <div className="flex items-start gap-2">
            <Package className="w-5 h-5 text-primary mt-0.5" />
            <div>
              <span className="font-medium">
                Selected Material{selectedMaterialIds.length > 1 ? 's' : ''} ({selectedMaterialIds.length}):
              </span>
              <div className="flex flex-wrap gap-2 mt-2">
                {selectedMaterialIds.map(id => {
                  const material = materials.find(m => m.id === id)
                  return material ? (
                    <Badge key={id} variant="outline" className="text-primary border-primary">
                      {material.name}
                    </Badge>
                  ) : null
                })}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}