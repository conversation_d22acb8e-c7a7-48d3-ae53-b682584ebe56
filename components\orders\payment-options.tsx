'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CreditCard, Clock, Shield, Zap } from 'lucide-react'

interface PaymentOptionsProps {
  selectedMode: 'pay_now' | 'pay_on_result' | null
  totalAmount: number
  onPaymentModeChange: (mode: 'pay_now' | 'pay_on_result') => void
}

export function PaymentOptions({ selectedMode, totalAmount, onPaymentModeChange }: PaymentOptionsProps) {
  const pickupFee = 25 // This should come from delivery options
  const discount = selectedMode === 'pay_now' ? totalAmount * 0.05 : 0 // 5% discount for pay now
  const finalAmount = totalAmount - discount

  return (
    <div className="space-y-6">
      {/* Payment Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Pay Now Option */}
        <Card
          className={`cursor-pointer transition-all hover:shadow-md ${
            selectedMode === 'pay_now'
              ? 'ring-2 ring-primary bg-primary/5'
              : 'hover:bg-accent/50'
          }`}
          onClick={() => onPaymentModeChange('pay_now')}
        >
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <CreditCard className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <CardTitle className="text-lg flex items-center gap-2">
                  Pay Now
                  <Badge variant="secondary" className="text-xs">
                    5% OFF
                  </Badge>
                </CardTitle>
                <CardDescription>Pay immediately and save money</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                <Zap className="w-4 h-4" />
                <span>Instant processing</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
                <Shield className="w-4 h-4" />
                <span>5% discount applied</span>
              </div>
              <div className="text-sm">
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>₹{totalAmount}</span>
                  </div>
                  <div className="flex justify-between text-green-600 dark:text-green-400">
                    <span>Discount (5%):</span>
                    <span>-₹{discount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between font-semibold border-t pt-1">
                    <span>Total:</span>
                    <span>₹{finalAmount.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pay on Result Option */}
        <Card
          className={`cursor-pointer transition-all hover:shadow-md ${
            selectedMode === 'pay_on_result'
              ? 'ring-2 ring-primary bg-primary/5'
              : 'hover:bg-accent/50'
          }`}
          onClick={() => onPaymentModeChange('pay_on_result')}
        >
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <Clock className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-lg">Pay on Result</CardTitle>
                <CardDescription>Pay when results are ready</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
                <Shield className="w-4 h-4" />
                <span>Pay only after completion</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
                <Clock className="w-4 h-4" />
                <span>Flexible payment timing</span>
              </div>
              <div className="text-sm">
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Total (due on completion):</span>
                    <span className="font-semibold">₹{totalAmount}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Methods Info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Accepted Payment Methods</CardTitle>
          <CardDescription>
            We accept various payment methods for your convenience
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2 p-3 border rounded-lg">
              <CreditCard className="w-5 h-5 text-muted-foreground" />
              <span className="text-sm font-medium">Credit Card</span>
            </div>
            <div className="flex items-center gap-2 p-3 border rounded-lg">
              <CreditCard className="w-5 h-5 text-muted-foreground" />
              <span className="text-sm font-medium">Debit Card</span>
            </div>
            <div className="flex items-center gap-2 p-3 border rounded-lg">
              <Shield className="w-5 h-5 text-muted-foreground" />
              <span className="text-sm font-medium">Bank Transfer</span>
            </div>
            <div className="flex items-center gap-2 p-3 border rounded-lg">
              <Zap className="w-5 h-5 text-muted-foreground" />
              <span className="text-sm font-medium">UPI</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selection Summary */}
      {selectedMode && (
        <div className="p-4 bg-primary/10 rounded-lg border border-primary/20">
          <div className="flex items-center gap-2 mb-3">
            <CreditCard className="w-5 h-5 text-primary" />
            <span className="font-medium">
              Selected: {selectedMode === 'pay_now' ? 'Pay Now' : 'Pay on Result'}
            </span>
            {selectedMode === 'pay_now' && (
              <Badge variant="secondary" className="ml-2">
                5% Discount Applied
              </Badge>
            )}
          </div>
          
          <div className="text-sm space-y-1">
            {selectedMode === 'pay_now' ? (
              <>
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>₹{totalAmount}</span>
                </div>
                <div className="flex justify-between text-green-600 dark:text-green-400">
                  <span>Discount (5%):</span>
                  <span>-₹{discount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-semibold border-t pt-1">
                  <span>Amount to pay now:</span>
                  <span className="text-primary">₹{finalAmount.toFixed(2)}</span>
                </div>
              </>
            ) : (
              <div className="flex justify-between font-semibold">
                <span>Amount due on completion:</span>
                <span className="text-primary">₹{totalAmount}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="text-sm text-muted-foreground bg-muted/50 p-4 rounded-lg">
        <p className="font-medium mb-2">💳 Payment Information:</p>
        <ul className="space-y-1 list-disc list-inside">
          <li><strong>Pay Now:</strong> Get 5% discount and immediate processing</li>
          <li><strong>Pay on Result:</strong> Pay only when your test results are ready</li>
          <li>All payments are processed securely through encrypted channels</li>
          <li>You'll receive a payment confirmation and receipt via email</li>
        </ul>
      </div>
    </div>
  )
}