'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Package, 
  TestTube, 
  MapPin, 
  Truck, 
  CreditCard, 
  Clock,
  CheckCircle,
  Loader2,
  IndianRupee
} from 'lucide-react'
import { createClient } from '@/lib/supabase'
import { useRouter } from 'next/navigation'

interface OrderData {
  materials: Array<{ id: string; name: string }>
  testTypes: Array<{ id: string; name: string; price: number }>
  deliveryType: 'pickup' | 'dropoff' | null
  address?: {
    line1: string
    city: string
    state: string
    pincode: string
  }
  paymentMode: 'pay_now' | 'pay_on_result' | null
  totalAmount: number
}

interface OrderSummaryProps {
  orderData: OrderData
  onSubmit: () => void
}

export function OrderSummary({ orderData, onSubmit }: OrderSummaryProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const router = useRouter()

  const pickupFee = orderData.deliveryType === 'dropoff' ? 500 : 0
  const discount = orderData.paymentMode === 'pay_now' ? orderData.totalAmount * 0.05 : 0
  const finalAmount = orderData.totalAmount + pickupFee - discount

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true)
      setSubmitError(null)
      
      const supabase = createClient()
      
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      if (userError || !user) {
        throw new Error('User not authenticated')
      }

      // Get user profile to get company_id
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('company_id')
        .eq('user_id', user.id)
        .single()

      if (profileError) {
        console.error('Profile error:', profileError)
        throw new Error('User profile not found')
      }

      // Create address if needed
      let addressId = null
      if (orderData.deliveryType === 'dropoff' && orderData.address) {
        const addressData = {
          company_id: profile?.company_id || null,
          line1: orderData.address.line1,
          city: orderData.address.city,
          state: orderData.address.state,
          pincode: orderData.address.pincode,
          country: 'India'
        }
        
        console.log('Inserting address with data:', addressData)
        
        const { data: insertedAddress, error: addressError } = await supabase
          .from('addresses')
          .insert(addressData)
          .select('id')
          .single()
        
        if (addressError) {
          console.error('Address insertion error:', addressError)
          throw addressError
        }
        
        addressId = insertedAddress.id
      }

      // Create the order
      const orderInsertData = {
        customer_id: user.id, // Use user ID, not company ID
        company_id: profile?.company_id || null,
        pickup_required: orderData.deliveryType === 'dropoff',
        pickup_address_id: addressId,
        pay_mode: orderData.paymentMode,
        status: 'draft',
        total_inr: finalAmount,
        notes: `Materials: ${orderData.materials.map(m => m.name).join(', ')}. Tests: ${orderData.testTypes.map(t => t.name).join(', ')}`
      }
      
      console.log('Inserting order with data:', orderInsertData)
      
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert(orderInsertData)
        .select('id')
        .single()

      if (orderError) throw orderError

      // Create order tests
      const orderTests = orderData.testTypes.map(test => ({
        order_id: order.id,
        test_type_id: test.id,
        price: test.price
      }))

      const { error: testsError } = await supabase
        .from('order_tests')
        .insert(orderTests)

      if (testsError) throw testsError

      // Create sample entries for each material
      const samples = orderData.materials.map(material => ({
        order_id: order.id,
        material_id: material.id,
        status: 'pending',
        received_date: new Date().toISOString()
      }))

      const { error: sampleError } = await supabase
        .from('samples')
        .insert(samples)

      if (sampleError) throw sampleError

      // Success - redirect to order confirmation
      router.push(`/orders/${order.id}?created=true`)
      
    } catch (error) {
      console.error('Error creating order:', error)
      setSubmitError(error instanceof Error ? error.message : 'Failed to create order')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Order Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Material & Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="w-5 h-5" />
              Material & Tests
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex flex-wrap items-center gap-2 mb-2">
                {orderData.materials.map((material) => (
                  <Badge key={material.id} variant="outline">{material.name}</Badge>
                ))}
              </div>
              <div className="space-y-2">
                {orderData.testTypes.map((test) => (
                  <div key={test.id} className="flex justify-between items-center text-sm">
                    <div className="flex items-center gap-2">
                      <TestTube className="w-4 h-4 text-muted-foreground" />
                      <span>{test.name}</span>
                    </div>
                    <span className="font-medium">₹{test.price}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Delivery */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {orderData.deliveryType === 'pickup' ? (
                <MapPin className="w-5 h-5" />
              ) : (
                <Truck className="w-5 h-5" />
              )}
              Delivery Method
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Badge variant={orderData.deliveryType === 'pickup' ? 'default' : 'secondary'}>
                  {orderData.deliveryType === 'pickup' ? 'Lab Pickup' : 'Sample Pickup'}
                </Badge>
                {orderData.deliveryType === 'dropoff' && (
                  <span className="text-sm text-muted-foreground">+₹500 fee</span>
                )}
              </div>
              
              {orderData.deliveryType === 'pickup' && (
                <div className="text-sm text-muted-foreground">
                  <p>Drop off at lab:</p>
                  <p>123 Science Park Drive<br />Research City, RC 12345</p>
                </div>
              )}
              
              {orderData.deliveryType === 'dropoff' && orderData.address && (
                <div className="text-sm text-muted-foreground">
                  <p>Pickup from:</p>
                  <p>
                    {orderData.address.line1}<br />
                    {orderData.address.city}, {orderData.address.state} {orderData.address.pincode}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment & Total */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Payment Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Badge variant={orderData.paymentMode === 'pay_now' ? 'default' : 'secondary'}>
                {orderData.paymentMode === 'pay_now' ? 'Pay Now' : 'Pay on Result'}
              </Badge>
              {orderData.paymentMode === 'pay_now' && (
                <Badge variant="outline" className="text-green-600 border-green-600">
                  5% Discount
                </Badge>
              )}
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Tests Subtotal:</span>
                <span>₹{orderData.totalAmount}</span>
              </div>
              
              {pickupFee > 0 && (
                <div className="flex justify-between">
                  <span>Pickup Fee:</span>
                  <span>+₹{pickupFee}</span>
                </div>
              )}
              
              {discount > 0 && (
                <div className="flex justify-between text-green-600 dark:text-green-400">
                  <span>Discount (5%):</span>
                  <span>-₹{discount.toFixed(2)}</span>
                </div>
              )}
              
              <Separator />
              
              <div className="flex justify-between font-semibold text-lg">
                <span>Total:</span>
                <span className="text-primary">₹{finalAmount.toFixed(2)}</span>
              </div>
              
              <div className="text-xs text-muted-foreground">
                {orderData.paymentMode === 'pay_now' ? 'Amount to be charged now' : 'Amount due on completion'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5" />
            Order Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <Badge variant="outline">Draft</Badge>
            <span className="text-sm text-muted-foreground">
              Order will be saved as draft and can be modified before final submission
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {submitError && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="text-destructive text-sm">
              <p className="font-medium">Error creating order:</p>
              <p>{submitError}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Submit Button */}
      <div className="flex justify-center">
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting}
          size="lg"
          className="min-w-48"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Creating Order...
            </>
          ) : (
            <>
              <CheckCircle className="w-4 h-4 mr-2" />
              Create Order
            </>
          )}
        </Button>
      </div>

      {/* Help Text */}
      <div className="text-sm text-muted-foreground bg-muted/50 p-4 rounded-lg">
        <p className="font-medium mb-2">📋 Next Steps:</p>
        <ul className="space-y-1 list-disc list-inside">
          <li>Order will be created in 'draft' status</li>
          <li>You can modify the order before final submission</li>
          <li>Lab will review and confirm the order details</li>
          <li>You'll receive email notifications about order progress</li>
        </ul>
      </div>
    </div>
  )
}