import { redirect } from 'next/navigation'
import { requireRole } from '@/lib/auth'
import { CreateOrderWizard } from '@/components/orders/create-order-wizard'

export default async function NewOrderPage() {
  // Only customers can create orders
  // This will redirect to login if unauthenticated, or to unauthorized if wrong role
  await requireRole(['customer'])

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Create New Order</h1>
        <p className="text-muted-foreground mt-2">
          Submit your materials for testing with our certified laboratory
        </p>
      </div>
      
      <CreateOrderWizard />
    </div>
  )
}