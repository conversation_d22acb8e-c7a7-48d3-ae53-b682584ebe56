'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { TestTube, Clock, IndianRupee, Search, Package, Gift } from 'lucide-react'
import { createClient } from '@/lib/supabase'

interface Material {
  id: string
  name: string
  description: string
  category: string
}

interface TestType {
  id: string
  name: string
  description: string
  price: number
  duration_days: number
  material_id: string
}

interface TestPackage {
  id: string
  name: string
  description: string
  material_id: string
  discount_percentage: number
  test_count: number
  total_price_inr: number
  discounted_price_inr: number
  savings_inr: number
  max_sla_days: number
  test_ids: string[]
}

interface SelectedTest {
  id: string
  name: string
  price: number
  material_id: string
}

interface MaterialWithTests {
  material: Material
  tests: TestType[]
  filteredTests: TestType[]
  packages: TestPackage[]
  filteredPackages: TestPackage[]
}

interface TestTypeSelectionProps {
  materialIds: string[]
  selectedTests: SelectedTest[]
  onTestsChange: (tests: SelectedTest[]) => void
}

export function TestTypeSelection({ materialIds, selectedTests, onTestsChange }: TestTypeSelectionProps) {
  const [materialsWithTests, setMaterialsWithTests] = useState<MaterialWithTests[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [openAccordionItems, setOpenAccordionItems] = useState<string[]>([])

  useEffect(() => {
    if (materialIds.length > 0) {
      fetchMaterialsAndTests()
    }
  }, [materialIds])

  // Compute filtered materials without causing re-renders
  const filteredMaterialsWithTests = materialsWithTests.map(materialWithTests => ({
    ...materialWithTests,
    filteredTests: materialWithTests.tests.filter(testType =>
      testType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      testType.description.toLowerCase().includes(searchTerm.toLowerCase())
    ),
    filteredPackages: materialWithTests.packages.filter(pkg =>
      pkg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pkg.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }))

  const fetchMaterialsAndTests = async () => {
    try {
      setLoading(true)
      const supabase = createClient()
      
      // Fetch materials
      const { data: materialsData, error: materialsError } = await supabase
        .from('materials')
        .select('*')
        .in('id', materialIds)
        .order('name', { ascending: true })

      if (materialsError) throw materialsError
      
      // Fetch test types for the selected materials
      const { data: testsData, error: testsError } = await supabase
        .from('test_types')
        .select('*')
        .in('material_id', materialIds)
        .order('name', { ascending: true })

      if (testsError) throw testsError

      // Fetch test packages for the selected materials
      const { data: packagesData, error: packagesError } = await supabase
        .from('v_test_packages_with_details')
        .select('*')
        .in('material_id', materialIds)
        .order('package_name', { ascending: true })

      if (packagesError) throw packagesError

      // Fetch package items to get test IDs for each package
      const packageIds = (packagesData || []).map(pkg => pkg.package_id)
      let packageItemsData: { package_id: string; test_type_id: string }[] = []
      if (packageIds.length > 0) {
        const { data: itemsData, error: itemsError } = await supabase
          .from('test_package_items')
          .select('package_id, test_type_id')
          .in('package_id', packageIds)
          .order('test_type_id', { ascending: true })
        
        if (itemsError) throw itemsError
        packageItemsData = itemsData || []
      }
      
      // Normalize materials
      const normalizedMaterials = (materialsData || []).map((m: any) => ({
        id: m.id,
        name: m.name,
        description: m.description || '',
        category: m.category || 'general'
      })) as Material[]
      
      // Normalize tests
      const normalizedTests = (testsData || []).map((t: any) => ({
        id: t.id,
        name: t.name,
        description: t.description || '',
        price: Number(t.base_price_inr ?? 0),
        duration_days: Number(t.sla_days ?? 0),
        material_id: t.material_id
      })) as TestType[]

      // Normalize packages
      const normalizedPackages = (packagesData || []).map((p: any) => {
        const packageTestIds = packageItemsData
          .filter(item => item.package_id === p.package_id)
          .map(item => item.test_type_id)
        
        return {
          id: p.package_id,
          name: p.package_name,
          description: p.package_description || '',
          material_id: p.material_id,
          discount_percentage: Number(p.discount_percentage ?? 0),
          test_count: Number(p.test_count ?? 0),
          total_price_inr: Number(p.total_price_inr ?? 0),
          discounted_price_inr: Number(p.discounted_price_inr ?? 0),
          savings_inr: Number(p.savings_inr ?? 0),
          max_sla_days: Number(p.max_sla_days ?? 0),
          test_ids: packageTestIds
        }
      }) as TestPackage[]

      // Group tests and packages by material
      const materialsWithTestsData: MaterialWithTests[] = normalizedMaterials.map(material => {
        const materialTests = normalizedTests.filter(test => test.material_id === material.id)
        const materialPackages = normalizedPackages.filter(pkg => pkg.material_id === material.id)
        return {
          material,
          tests: materialTests,
          filteredTests: materialTests,
          packages: materialPackages,
          filteredPackages: materialPackages
        }
      })

      setMaterialsWithTests(materialsWithTestsData)
    } catch (err) {
      console.error('Error fetching materials and tests:', err)
      setError('Failed to load materials and tests. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleTestToggle = (testType: TestType, checked: boolean) => {
    if (checked) {
      // Add test
      const newTest: SelectedTest = {
        id: testType.id,
        name: testType.name,
        price: testType.price,
        material_id: testType.material_id
      }
      onTestsChange([...selectedTests, newTest])
    } else {
      // Remove test
      onTestsChange(selectedTests.filter(test => test.id !== testType.id))
    }
  }

  const handlePackageToggle = (pkg: TestPackage, checked: boolean) => {
    if (checked) {
      // Add all tests from the package
      const materialWithTests = materialsWithTests.find(m => m.material.id === pkg.material_id)
      if (!materialWithTests) return
      
      const packageTests = materialWithTests.tests.filter(test => pkg.test_ids.includes(test.id))
      const newTests: SelectedTest[] = packageTests.map(test => ({
        id: test.id,
        name: test.name,
        price: test.price,
        material_id: test.material_id
      }))
      
      // Remove any existing tests from this material and add package tests
      const otherTests = selectedTests.filter(test => test.material_id !== pkg.material_id)
      onTestsChange([...otherTests, ...newTests])
    } else {
      // Remove all tests from this package
      onTestsChange(selectedTests.filter(test => !pkg.test_ids.includes(test.id)))
    }
  }

  const isTestSelected = (testId: string) => {
    return selectedTests.some(test => test.id === testId)
  }

  const isPackageSelected = (pkg: TestPackage) => {
    return pkg.test_ids.length > 0 && pkg.test_ids.every(testId => isTestSelected(testId))
  }

  const totalPrice = selectedTests.reduce((sum, test) => sum + test.price, 0)

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-destructive mb-4">{error}</div>
        <Button onClick={fetchMaterialsAndTests} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  if (materialsWithTests.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <TestTube className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p>No test types available for the selected materials.</p>
        <p className="text-sm mt-2">Please contact support if you need specific tests.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search tests by name or description..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Materials with Tests - Accordion */}
      <Accordion 
        type="multiple" 
        value={openAccordionItems}
        onValueChange={setOpenAccordionItems}
        className="space-y-4"
      >
        {filteredMaterialsWithTests.map((materialWithTests) => {
          const materialTests = searchTerm ? materialWithTests.filteredTests : materialWithTests.tests
          const selectedTestsForMaterial = selectedTests.filter(test => test.material_id === materialWithTests.material.id)
          
          return (
            <AccordionItem key={materialWithTests.material.id} value={materialWithTests.material.id} className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline">
                <div className="flex items-center gap-3 text-left">
                  <Package className="w-5 h-5 text-primary" />
                  <div>
                    <div className="font-medium">{materialWithTests.material.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {materialTests.length} tests available
                      {selectedTestsForMaterial.length > 0 && (
                        <span className="ml-2 text-primary font-medium">
                          • {selectedTestsForMaterial.length} selected
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                {materialTests.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <TestTube className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>No tests found for "{searchTerm}"</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* Test Packages Section */}
                    {materialWithTests.filteredPackages.length > 0 && (
                      <div>
                        <div className="flex items-center gap-2 mb-3">
                          <Gift className="w-4 h-4 text-primary" />
                          <h4 className="text-sm font-semibold text-primary">Test Packages (Recommended)</h4>
                        </div>
                        <div className="overflow-x-auto">
                          <div className="flex gap-4 pb-4 min-w-max">
                            {materialWithTests.filteredPackages.map((pkg) => {
                              const isSelected = isPackageSelected(pkg)
                              
                              return (
                                <Card
                                  key={pkg.id}
                                  className={`cursor-pointer transition-all hover:shadow-md min-w-[320px] flex-shrink-0 border-primary/20 ${
                                    isSelected
                                      ? 'ring-2 ring-primary bg-primary/5'
                                      : 'hover:bg-accent/50'
                                  }`}
                                  onClick={() => handlePackageToggle(pkg, !isSelected)}
                                >
                                  <CardHeader className="pb-3">
                                    <div className="flex items-start justify-between">
                                      <div className="flex items-center gap-3">
                                        <Checkbox
                                          checked={isSelected}
                                          onChange={() => {}} // Handled by card click
                                          className="mt-1"
                                        />
                                        <div>
                                          <CardTitle className="text-base">
                                            {pkg.name}
                                            <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                                              {pkg.test_count} tests
                                            </span>
                                          </CardTitle>
                                          <div className="flex items-center gap-4 mt-2">
                                            <div className="flex items-center gap-1 text-sm text-primary font-medium">
                                              <IndianRupee className="w-4 h-4" />
                                              <span>₹{pkg.discounted_price_inr}</span>
                                            </div>
                                            <div className="flex items-center gap-1 text-sm text-muted-foreground line-through">
                                              <span>₹{pkg.total_price_inr}</span>
                                            </div>
                                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                              <Clock className="w-4 h-4" />
                                              <span>{pkg.max_sla_days} days</span>
                                            </div>
                                          </div>
                                          <div className="mt-2">
                                            <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                              Save ₹{pkg.savings_inr} ({pkg.discount_percentage}% off)
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                      {isSelected && (
                                        <Badge variant="default">
                                          Selected
                                        </Badge>
                                      )}
                                    </div>
                                  </CardHeader>
                                  <CardContent>
                                    <CardDescription className="text-sm">
                                      {pkg.description}
                                    </CardDescription>
                                  </CardContent>
                                </Card>
                              )
                            })}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Individual Tests Section */}
                    <div>
                      <h4 className="text-sm font-semibold mb-3">Individual Tests</h4>
                      <div className="overflow-x-auto">
                        <div className="flex gap-4 pb-4 min-w-max">
                          {materialTests.map((testType) => {
                            const isSelected = isTestSelected(testType.id)
                            
                            return (
                              <Card
                                key={testType.id}
                                className={`cursor-pointer transition-all hover:shadow-md min-w-[320px] flex-shrink-0 ${
                                  isSelected
                                    ? 'ring-2 ring-primary bg-primary/5'
                                    : 'hover:bg-accent/50'
                                }`}
                                onClick={() => handleTestToggle(testType, !isSelected)}
                              >
                                <CardHeader className="pb-3">
                                  <div className="flex items-start justify-between">
                                    <div className="flex items-center gap-3">
                                      <Checkbox
                                        checked={isSelected}
                                        onChange={() => {}} // Handled by card click
                                        className="mt-1"
                                      />
                                      <div>
                                        <CardTitle className="text-base">{testType.name}</CardTitle>
                                        <div className="flex items-center gap-4 mt-2">
                                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                            <IndianRupee className="w-4 h-4" />
                                            <span>₹{testType.price}</span>
                                          </div>
                                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                            <Clock className="w-4 h-4" />
                                            <span>{testType.duration_days} days</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                    {isSelected && (
                                      <Badge variant="default">
                                        Selected
                                      </Badge>
                                    )}
                                  </div>
                                </CardHeader>
                                <CardContent>
                                  <CardDescription className="text-sm">
                                    {testType.description}
                                  </CardDescription>
                                </CardContent>
                              </Card>
                            )
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          )
        })}
      </Accordion>

      {/* Selection Summary */}
      {selectedTests.length > 0 && (
        <div className="mt-6 p-4 bg-primary/10 rounded-lg border border-primary/20">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <TestTube className="w-5 h-5 text-primary" />
              <span className="font-medium">Selected Tests ({selectedTests.length}):</span>
            </div>
            
            <div className="space-y-4">
              {filteredMaterialsWithTests.map((materialWithTests) => {
                const materialSelectedTests = selectedTests.filter(test => test.material_id === materialWithTests.material.id)
                if (materialSelectedTests.length === 0) return null
                
                return (
                  <div key={materialWithTests.material.id} className="space-y-2">
                    <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                      <Package className="w-4 h-4" />
                      <span>{materialWithTests.material.name}</span>
                    </div>
                    <div className="space-y-1 ml-6">
                      {materialSelectedTests.map((test) => (
                        <div key={test.id} className="flex justify-between items-center text-sm">
                          <span>{test.name}</span>
                          <span className="font-medium">₹{test.price}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )
              })}
            </div>
            
            <div className="border-t pt-2 flex justify-between items-center font-semibold">
              <span>Total:</span>
              <span className="text-primary">₹{totalPrice}</span>
            </div>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="text-sm text-muted-foreground bg-muted/50 p-4 rounded-lg">
        <p className="font-medium mb-2">💡 Test Selection Tips:</p>
        <ul className="space-y-1 list-disc list-inside">
          <li>Select multiple tests to get comprehensive analysis</li>
          <li>Duration shown is typical turnaround time</li>
          <li>Prices are per sample and may vary based on complexity</li>
        </ul>
      </div>
    </div>
  )
}