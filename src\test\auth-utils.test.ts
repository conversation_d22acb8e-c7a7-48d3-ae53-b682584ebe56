import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { getCurrentSession, signUpUser, waitForSession } from '@/lib/auth-utils'
import { createClient } from '@/utils/supabase/client'

// Mock the Supabase client
vi.mock('@/utils/supabase/client', () => ({
  createClient: vi.fn()
}))

const mockSupabase = {
  auth: {
    getSession: vi.fn(),
    signUp: vi.fn()
  }
}

describe('Auth Utils', () => {
  beforeEach(() => {
    vi.mocked(createClient).mockReturnValue(mockSupabase as any)
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getCurrentSession', () => {
    it('should return session when successful', async () => {
      const mockSession = { user: { id: '123', email: '<EMAIL>' } }
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: mockSession },
        error: null
      })

      const result = await getCurrentSession()
      expect(result).toEqual(mockSession)
    })

    it('should return null when there is an error', async () => {
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: new Error('Session error')
      })

      const result = await getCurrentSession()
      expect(result).toBeNull()
    })

    it('should return null when exception is thrown', async () => {
      mockSupabase.auth.getSession.mockRejectedValue(new Error('Network error'))

      const result = await getCurrentSession()
      expect(result).toBeNull()
    })
  })

  describe('signUpUser', () => {
    it('should successfully sign up user with metadata', async () => {
      const mockData = { user: { id: '123', email: '<EMAIL>' } }
      const metadata = { first_name: 'Test', last_name: 'User', role: 'customer' }
      
      mockSupabase.auth.signUp.mockResolvedValue({
        data: mockData,
        error: null
      })

      const result = await signUpUser('<EMAIL>', 'password123', metadata)
      
      expect(mockSupabase.auth.signUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        options: {
          data: metadata
        }
      })
      expect(result).toEqual(mockData)
    })

    it('should throw error when signup fails', async () => {
      const mockError = new Error('Signup failed')
      mockSupabase.auth.signUp.mockResolvedValue({
        data: null,
        error: mockError
      })

      await expect(signUpUser('<EMAIL>', 'password123', {})).rejects.toThrow('Signup failed')
    })

    it('should throw error when exception occurs', async () => {
      mockSupabase.auth.signUp.mockRejectedValue(new Error('Network error'))

      await expect(signUpUser('<EMAIL>', 'password123', {})).rejects.toThrow('Network error')
    })
  })

  describe('waitForSession', () => {
    it('should return session when found on first attempt', async () => {
      const mockSession = { user: { id: '123', email: '<EMAIL>' } }
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: mockSession },
        error: null
      })

      const result = await waitForSession(3, 100)
      expect(result).toEqual(mockSession)
      expect(mockSupabase.auth.getSession).toHaveBeenCalledTimes(1)
    })

    it('should return session after multiple attempts', async () => {
      const mockSession = { user: { id: '123', email: '<EMAIL>' } }
      mockSupabase.auth.getSession
        .mockResolvedValueOnce({ data: { session: null }, error: null })
        .mockResolvedValueOnce({ data: { session: null }, error: null })
        .mockResolvedValueOnce({ data: { session: mockSession }, error: null })

      const result = await waitForSession(3, 10)
      expect(result).toEqual(mockSession)
      expect(mockSupabase.auth.getSession).toHaveBeenCalledTimes(3)
    })

    it('should return null when max attempts reached', async () => {
      mockSupabase.auth.getSession.mockResolvedValue({
        data: { session: null },
        error: null
      })

      const result = await waitForSession(2, 10)
      expect(result).toBeNull()
      expect(mockSupabase.auth.getSession).toHaveBeenCalledTimes(2)
    })
  })
})