'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { createClient } from '@/utils/supabase/client'
import { UserRole } from '@/lib/auth'
import { User as SupabaseUser } from '@supabase/supabase-js'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import {
  User,
  LogOut,
  Settings,
  Home,
  Package,
  FlaskConical,
  Truck,
  Shield,
  DollarSign,
  FileText,
  Menu,
  X
} from 'lucide-react'

interface NavbarProps {
  initialUser?: SupabaseUser | null
  initialUserRole?: UserRole | null
}

interface NavItem {
  label: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  roles: UserRole[]
}

const navigationItems: NavItem[] = [
  {
    label: 'Dashboard',
    href: '/dashboard',
    icon: Home,
    roles: ['customer', 'tech', 'pickup', 'admin', 'finance', 'auditor']
  },
  {
    label: 'Orders',
    href: '/orders',
    icon: Package,
    roles: ['customer', 'admin']
  },
  {
    label: 'Lab',
    href: '/lab',
    icon: FlaskConical,
    roles: ['tech', 'admin']
  },
  {
    label: 'Pickup',
    href: '/pickup',
    icon: Truck,
    roles: ['pickup', 'admin']
  },
  {
    label: 'Admin',
    href: '/admin',
    icon: Shield,
    roles: ['admin']
  },
  {
    label: 'Finance',
    href: '/admin/finance',
    icon: DollarSign,
    roles: ['finance', 'admin']
  },
  {
    label: 'Audit',
    href: '/admin/audit',
    icon: FileText,
    roles: ['auditor', 'admin']
  }
]

export function Navbar({ initialUser = null, initialUserRole = null }: NavbarProps) {
  const [user, setUser] = useState<SupabaseUser | null>(initialUser)
  const [userRole, setUserRole] = useState<UserRole | null>(initialUserRole)
  const [loading, setLoading] = useState(!initialUser)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const router = useRouter()
  const pathname = usePathname()
  const supabase = createClient()

  useEffect(() => {
    if (!supabase) {
      setLoading(false)
      return
    }

    async function getUser() {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (user) {
        setUser(user)
        
        // Get user profile for role
        const { data: profile } = await supabase
          .from('profiles')
          .select('role, display_name')
          .eq('user_id', user.id)
          .single()
        
        if (profile) {
          setUserRole(profile.role as UserRole)
        }
      }
      
      setLoading(false)
    }

    // Only fetch user if we don't have initial data
    if (!initialUser) {
      getUser()
    } else {
      // If we have initial user but no role, fetch the role
      if (!initialUserRole && initialUser) {
        async function getUserRole() {
          const { data: profile } = await supabase
            .from('profiles')
            .select('role, display_name')
            .eq('user_id', initialUser?.id ?? '')
            .single()
          
          if (profile) {
            setUserRole(profile.role as UserRole)
          }
          setLoading(false)
        }
        getUserRole()
      }
    }

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_OUT') {
        setUser(null)
        setUserRole(null)
        router.push('/login')
      } else if (event === 'SIGNED_IN' && session?.user) {
        getUser()
      }
    })

    return () => subscription.unsubscribe()
  }, [supabase, router, initialUser, initialUserRole])

  const handleSignOut = async () => {
    if (!supabase) return
    
    try {
      await supabase.auth.signOut()
      router.push('/login')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  // Filter navigation items based on user role
  const allowedNavItems = navigationItems.filter(item => 
    userRole && item.roles.includes(userRole)
  )

  // Don't show navbar on auth pages
  const authPages = ['/login', '/signup', '/forgot-password', '/reset-password']
  if (authPages.includes(pathname)) {
    return null
  }

  // Show loading skeleton while checking auth
  if (loading || !user) {
    return (
      <nav className="sticky top-0 z-50 bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 bg-gray-200 rounded animate-pulse" />
                <div className="h-6 w-32 bg-gray-200 rounded animate-pulse" />
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="h-8 w-20 bg-gray-200 rounded animate-pulse" />
              <div className="h-8 w-8 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        </div>
      </nav>
    )
  }

  return (
    <nav className="sticky top-0 z-50 bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and Navigation */}
          <div className="flex items-center">
            <Link href="/dashboard" className="flex items-center space-x-2">
              <FlaskConical className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">GeoGroup Labs</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:ml-8 md:flex md:space-x-4">
              {allowedNavItems.map((item) => {
                const IconComponent = item.icon
                const isActive = pathname.startsWith(item.href)
                
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <IconComponent className="h-4 w-4" />
                    <span>{item.label}</span>
                  </Link>
                )
              })}
            </div>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {/* Role Badge */}
            {userRole && (
              <Badge variant="secondary" className="hidden sm:inline-flex">
                {userRole.charAt(0).toUpperCase() + userRole.slice(1)}
              </Badge>
            )}

            {/* User Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span className="hidden sm:inline">
                    {user.user_metadata?.first_name || user.user_metadata?.name || user.email}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium">
                      {user.user_metadata?.first_name || user.user_metadata?.name || user.email}
                    </p>
                    <p className="text-xs text-gray-500">{user.email}</p>
                    {userRole && (
                      <Badge variant="secondary" className="w-fit">
                        {userRole.charAt(0).toUpperCase() + userRole.slice(1)}
                      </Badge>
                    )}
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="flex items-center">
                    <Settings className="mr-2 h-4 w-4" />
                    Profile Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <div className="space-y-2">
              {allowedNavItems.map((item) => {
                const IconComponent = item.icon
                const isActive = pathname.startsWith(item.href)
                
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <IconComponent className="h-4 w-4" />
                    <span>{item.label}</span>
                  </Link>
                )
              })}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}