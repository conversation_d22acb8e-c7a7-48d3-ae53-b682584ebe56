# Database Setup Instructions

## Quick Setup

The login redirect issue you're experiencing is caused by missing database tables. Follow these steps to set up your Supabase database:

### Step 1: Access Supabase SQL Editor

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `fjetpirulsoodntxfopa`
3. Navigate to **SQL Editor** in the left sidebar

### Step 2: Run Database Setup

1. Copy the entire contents of `setup-database.sql`
2. Paste it into the SQL Editor
3. Click **Run** to execute the script

### Step 3: Verify Setup

After running the script, you should see:
- ✅ `profiles` table created
- ✅ `companies` table created  
- ✅ `labs` table created
- ✅ User roles enum created
- ✅ Row Level Security policies applied
- ✅ Automatic profile creation trigger set up

### Step 4: Test Authentication

1. Try signing up a new user at `http://localhost:3000/signup`
2. After successful signup, you should be redirected to the dashboard
3. Login should now work without redirecting back to the login page

## What This Fixes

The database setup creates:

1. **profiles table**: Stores user role and company information
2. **companies table**: Stores company details for customers
3. **labs table**: Stores lab information for technicians
4. **Automatic profile creation**: Trigger that creates a profile when a user signs up
5. **Row Level Security**: Ensures users can only access their own data

## Troubleshooting

If you still experience issues after setup:

1. **Check the browser console** for any JavaScript errors
2. **Verify environment variables** in `.env.local` match your Supabase project
3. **Test the database connection** by checking if tables exist in Supabase Table Editor

## Alternative: Local Development

For local development with Docker:

```bash
# Install Docker Desktop first
npx supabase start
npx supabase db reset
```

This will create a local Supabase instance with the migration files.