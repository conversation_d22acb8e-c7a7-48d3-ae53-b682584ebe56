import { createClient } from '@/utils/supabase/client'

// Helper function to safely get current session
export const getCurrentSession = async () => {
  try {
    const supabase = createClient()
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      console.error('Session error:', error)
      return null
    }
    
    return session
  } catch (error) {
    console.error('Failed to get session:', error)
    return null
  }
}

// Helper function to safely sign up user
type SignupMetadata = {
  first_name?: string
  last_name?: string
  phone?: string
  role?: string
  [key: string]: unknown
}

export const signUpUser = async (email: string, password: string, metadata: SignupMetadata) => {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    })
    
    if (error) {
      throw error
    }
    
    return data
  } catch (error) {
    console.error('Signup error:', error)
    throw error
  }
}

// Helper function to wait for session establishment
export const waitForSession = async (maxAttempts = 5, delay = 1000) => {
  for (let i = 0; i < maxAttempts; i++) {
    const session = await getCurrentSession()
    if (session) {
      return session
    }
    await new Promise(resolve => setTimeout(resolve, delay))
  }
  return null
}