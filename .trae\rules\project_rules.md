# AI Development Rules for GeoGroup Lab Testing SaaS

These are **non‑negotiable rules** the AI must follow while generating or modifying code for this project.

---

## 📦 Tech Stack Compliance
1. **Frontend:** Next.js (latest stable), React 18+.
2. **Styling:** Only use [shadcn/ui](https://ui.shadcn.com) components + TailwindCSS. No other UI libs.
3. **Backend:** Supabase (auth, DB, storage) with SSR packages enabled (`@supabase/ssr`).
4. **Deployment:** Vercel (edge-friendly, SSR-ready).
5. **Email:** Gmail SMTP (via `nodemailer`).  
6. **Payments:** Razorpay (future-proof integration).

---

## 🎨 Code Style
1. **Formatting:** Use `standardjs` (no semicolons, 2-space indentation).
2. **Linting:** Ensure ESLint with StandardJS rules is respected.
3. **Naming:** 
   - Components: `PascalCase`
   - Functions/variables: `camelCase`
   - Constants: `UPPER_SNAKE_CASE`
4. **File Size:** No file should exceed **200 lines**. Split logic into smaller modules if needed.
5. **Comments:** Minimal, meaningful. Avoid redundant comments.

---

## 📱 UI/UX Rules
1. **Responsive by default** (mobile-first design).
2. Use `flex`, `grid`, and Tailwind utilities to achieve responsiveness.
3. Dark mode support (via Tailwind `dark:` classes).
4. Avoid inline styles. Use utility classes or component props.

---

## 🏗️ Architecture
1. **Pages vs Components:**
   - `app/` directory (Next.js App Router).
   - Keep UI in `components/`.
   - Keep business logic in `lib/` or `services/`.
2. **Database Access:** Only via Supabase client wrapped in `lib/supabase.ts`.
3. **State Management:** Use React hooks or context where needed. No Redux unless justified.
4. **Code Modularity:** Break down large logic into reusable hooks/components.
5. **SSR/SSG Compliance:** Use Next.js `server` and `client` components correctly.

---

## 🔐 Security & Auth
1. Use Supabase Auth for user authentication/authorization.
2. Implement Row-Level Security (RLS) in Supabase for all data access.
3. Never expose service role keys to the client.
4. Validate all inputs before database writes.

---

## ✅ Testing
1. Use **Playwright** and **Puppeteer** for E2E tests.
2. Unit tests required for core logic (Jest or Vitest).
3. Run **TestSprite** for automated test generation.
4. Ensure 80%+ coverage where applicable.

---

## 🚀 Performance
1. Use React Suspense & streaming where possible.
2. Lazy-load heavy components.
3. Avoid unnecessary dependencies.
4. Cache expensive calls with Supabase caching or Next.js revalidation.

---

## 🔄 Workflow
1. Always modularize new features under `features/` folder.
2. Maintain **clean commit history** with clear messages.
3. Respect Git hooks (lint + test before commit).

---

✅ **End of Rules — The AI must follow these at all times when generating code for this repo.**
