import { createClient } from '@/utils/supabase/server'
import { Navbar } from './navbar'
import { UserRole } from '@/lib/auth'

export async function NavbarWrapper() {
  const supabase = await createClient()
  
  try {
    // Get user session from server
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return <Navbar />
    }

    // Get user profile for role
    const { data: profile } = await supabase
      .from('profiles')
      .select('role, display_name')
      .eq('user_id', user.id)
      .single()

    const userRole = profile?.role as UserRole || null

    return <Navbar initialUser={user} initialUserRole={userRole} />
  } catch (error) {
    console.error('Error fetching initial auth state:', error)
    return <Navbar />
  }
}